// DataTables 滚动和点击问题修复脚本 - 强力版本
$(document).ready(function() {
  
  console.log('DataTables滚动和点击问题修复脚本开始加载...');
  
  // 强制修复函数
  function forceFixDataTables() {
    console.log('执行强制修复...');
    
    // 使用!important强制设置样式
    $('<style id="datatables-force-fix">')
      .text(`
        .dataTables_wrapper { position: relative !important; z-index: 1 !important; }
        .dataTables_filter { position: sticky !important; top: 0 !important; z-index: 9999 !important; background: white !important; padding: 10px !important; margin-bottom: 10px !important; border-bottom: 1px solid #ddd !important; }
        .dataTables_filter input { position: relative !important; z-index: 10000 !important; background: white !important; border: 1px solid #ced4da !important; border-radius: 4px !important; padding: 6px 12px !important; min-width: 200px !important; }
        .dataTables_length { position: relative !important; z-index: 9999 !important; background: white !important; padding: 10px !important; margin-bottom: 10px !important; }
        .dataTables_length select { position: relative !important; z-index: 10000 !important; background: white !important; }
        .dataTables_info { position: relative !important; z-index: 9999 !important; background: white !important; padding: 10px !important; }
        .dataTables_paginate { position: relative !important; z-index: 9999 !important; background: white !important; padding: 10px !important; }
        .dataTables_paginate .paginate_button { position: relative !important; z-index: 10000 !important; background: white !important; }
        .dt-buttons { position: relative !important; z-index: 9999 !important; background: white !important; padding: 10px !important; margin-bottom: 10px !important; }
        .dt-buttons .dt-button { position: relative !important; z-index: 10000 !important; }
        .dataTables_scrollHead { position: relative !important; z-index: 999 !important; }
        .dataTables_scrollBody { position: relative !important; z-index: 1 !important; overflow-x: auto !important; overflow-y: auto !important; }
        .dataTables_scroll { overflow: visible !important; }
        .dataTable { width: 100% !important; }
        .dataTable th, .dataTable td { white-space: nowrap !important; overflow: hidden !important; text-overflow: ellipsis !important; }
      `)
      .appendTo('head');
    
    // 强制设置DOM元素的样式
    $('.dataTables_filter').each(function() {
      $(this).css({
        'position': 'sticky',
        'top': '0',
        'z-index': '9999',
        'background': 'white',
        'padding': '10px',
        'margin-bottom': '10px',
        'border-bottom': '1px solid #ddd'
      });
    });
    
    $('.dataTables_filter input').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '10000',
        'background': 'white',
        'border': '1px solid #ced4da',
        'border-radius': '4px',
        'padding': '6px 12px',
        'min-width': '200px'
      });
    });
    
    $('.dataTables_length').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'padding': '10px',
        'margin-bottom': '10px'
      });
    });
    
    $('.dataTables_length select').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '10000',
        'background': 'white'
      });
    });
    
    $('.dataTables_info').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'padding': '10px'
      });
    });
    
    $('.dataTables_paginate').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'padding': '10px'
      });
    });
    
    $('.dataTables_paginate .paginate_button').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '10000',
        'background': 'white'
      });
    });
    
    $('.dt-buttons').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'padding': '10px',
        'margin-bottom': '10px'
      });
    });
    
    $('.dt-buttons .dt-button').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '10000'
      });
    });
    
    $('.dataTables_scrollHead').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '999'
      });
    });
    
    $('.dataTables_scrollBody').each(function() {
      $(this).css({
        'position': 'relative',
        'z-index': '1',
        'overflow-x': 'auto',
        'overflow-y': 'auto'
      });
    });
    
    $('.dataTables_scroll').each(function() {
      $(this).css('overflow', 'visible');
    });
    
    console.log('强制修复完成');
  }
  
  // 监听所有可能的DataTables事件
  $(document).on('init.dt', function(e, settings) {
    console.log('DataTables初始化事件触发');
    setTimeout(forceFixDataTables, 100);
  });
  
  $(document).on('draw.dt', function(e, settings) {
    console.log('DataTables重绘事件触发');
    setTimeout(forceFixDataTables, 100);
  });
  
  $(document).on('page.dt', function(e, settings) {
    console.log('DataTables分页事件触发');
    setTimeout(forceFixDataTables, 100);
  });
  
  $(document).on('search.dt', function(e, settings) {
    console.log('DataTables搜索事件触发');
    setTimeout(forceFixDataTables, 100);
  });
  
  $(document).on('order.dt', function(e, settings) {
    console.log('DataTables排序事件触发');
    setTimeout(forceFixDataTables, 100);
  });
  
  // 监听DOM变化
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1 && $(node).find('.dataTables_wrapper').length > 0) {
            console.log('检测到新的DataTables元素');
            setTimeout(forceFixDataTables, 200);
          }
        });
      }
    });
  });
  
  // 开始观察DOM变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 监听窗口大小变化
  $(window).resize(function() {
    console.log('窗口大小变化，重新修复');
    setTimeout(forceFixDataTables, 100);
  });
  
  // 监听滚动事件
  $(document).on('scroll', function() {
    // 检查是否有DataTables元素需要修复
    if ($('.dataTables_wrapper').length > 0) {
      setTimeout(forceFixDataTables, 50);
    }
  });
  
  // 强制点击事件修复
  $(document).on('click', '.dataTables_filter input', function(e) {
    e.stopPropagation();
    $(this).css({
      'z-index': '10001',
      'position': 'relative'
    });
  });
  
  $(document).on('focus', '.dataTables_filter input', function() {
    $(this).css({
      'z-index': '10001',
      'position': 'relative'
    });
  });
  
  // 修复表格行点击
  $(document).on('click', '.dataTable tbody tr', function(e) {
    if (!$(e.target).is('input, select, textarea, button, a')) {
      $(this).toggleClass('selected');
    }
  });
  
  // 修复表格头部点击
  $(document).on('click', '.dataTable thead th', function(e) {
    if ($(this).hasClass('sorting') || $(this).hasClass('sorting_asc') || $(this).hasClass('sorting_desc')) {
      $(this).css({
        'position': 'relative',
        'z-index': '1000'
      });
    }
  });
  
  // 定期检查和修复
  setInterval(function() {
    if ($('.dataTables_wrapper').length > 0) {
      forceFixDataTables();
    }
  }, 2000);
  
  // 初始化时执行一次
  setTimeout(forceFixDataTables, 500);
  
  // 等待页面完全加载后再次执行
  $(window).on('load', function() {
    setTimeout(forceFixDataTables, 1000);
  });
  
  console.log('DataTables滚动和点击问题修复脚本加载完成');
}); 