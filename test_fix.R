# 测试修复后的描述性统计功能
# Test Fixed Descriptive Statistics Functions

# 设置工作目录
setwd("medical_analysis_system")

# 加载必要的库
suppressMessages({
  library(tableone)
  library(car)
})

# 加载函数
source("modules/statistical_analysis.R")
source("utils/helpers.R")

# 创建简单的测试数据
cat("创建测试数据...\n")
test_data <- data.frame(
  patient_id = 1:50,  # 这个变量应该被排除
  age = rnorm(50, 65, 10),
  gender = sample(c("Male", "Female"), 50, replace = TRUE),
  creatinine = rnorm(50, 1.2, 0.3),
  group = sample(c("A", "B"), 50, replace = TRUE),
  constant_var = rep(1, 50),  # 常数变量，应该被跳过
  stringsAsFactors = FALSE
)

# 添加一些缺失值
test_data$creatinine[sample(1:50, 5)] <- NA

cat("测试数据创建完成\n")
cat("数据维度:", dim(test_data), "\n")
cat("包含patient_id:", "patient_id" %in% names(test_data), "\n")
cat("包含常数变量:", "constant_var" %in% names(test_data), "\n")

# 测试1: 正态性检验（不分组）
cat("\n=== 测试1: 正态性检验（不分组）===\n")
tryCatch({
  result1 <- perform_descriptive_analysis(
    data = test_data,
    show_missing = FALSE,
    show_normal_test = TRUE,
    show_variance_test = FALSE
  )
  
  if (!is.null(result1$normality_tests)) {
    cat("✓ 正态性检验成功\n")
    cat("检验的变量:", paste(unique(result1$normality_tests$Variable), collapse = ", "), "\n")
    cat("是否包含patient_id:", "patient_id" %in% result1$normality_tests$Variable, "\n")
  } else {
    cat("✗ 正态性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 正态性检验失败:", e$message, "\n")
})

# 测试2: 方差齐性检验
cat("\n=== 测试2: 方差齐性检验 ===\n")

# 首先单独测试方差齐性检验函数
cat("直接测试方差齐性检验函数...\n")
tryCatch({
  numeric_vars <- c("age", "creatinine")
  variance_result <- perform_variance_tests(test_data, numeric_vars, "group")

  if (!is.null(variance_result) && nrow(variance_result) > 0) {
    cat("✓ 直接调用方差齐性检验成功\n")
    print(variance_result)
  } else {
    cat("✗ 直接调用方差齐性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 直接调用方差齐性检验失败:", e$message, "\n")
  cat("详细错误信息:", toString(e), "\n")
})

# 然后测试完整的描述性统计分析
cat("\n通过描述性统计分析测试...\n")
tryCatch({
  result2 <- perform_descriptive_analysis(
    data = test_data,
    group_var = "group",
    show_missing = FALSE,
    show_normal_test = FALSE,
    show_variance_test = TRUE
  )

  if (!is.null(result2$variance_tests)) {
    cat("✓ 方差齐性检验成功\n")
    cat("检验的变量:", paste(unique(result2$variance_tests$Variable), collapse = ", "), "\n")
    cat("是否包含patient_id:", "patient_id" %in% result2$variance_tests$Variable, "\n")
  } else {
    cat("✗ 方差齐性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 方差齐性检验失败:", e$message, "\n")
  cat("详细错误信息:", toString(e), "\n")
})

# 测试3: 完整测试
cat("\n=== 测试3: 完整功能测试 ===\n")
tryCatch({
  result3 <- perform_descriptive_analysis(
    data = test_data,
    group_var = "group",
    show_missing = TRUE,
    show_normal_test = TRUE,
    show_variance_test = TRUE
  )
  
  cat("✓ 完整功能测试成功\n")
  cat("可用组件:", paste(names(result3), collapse = ", "), "\n")
  
  if (!is.null(result3$missing_analysis)) {
    cat("缺失值分析: ✓\n")
  }
  if (!is.null(result3$normality_tests)) {
    cat("正态性检验: ✓\n")
  }
  if (!is.null(result3$variance_tests)) {
    cat("方差齐性检验: ✓\n")
  }
  
}, error = function(e) {
  cat("✗ 完整功能测试失败:", e$message, "\n")
})

cat("\n=== 测试完成 ===\n")
