# 医学数据分析系统 - 数据管理界面
# Medical Data Analysis System - Data Management UI

# 数据上传界面
ui_data_upload <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("upload", style = "margin-right: 15px;"),
          "数据上传",
          style = "margin: 0; font-weight: 300;"
        ),
        p("上传您的医学数据文件进行分析", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    # 上传区域
    column(8,
      box(
        title = "文件上传",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          # 文件输入
          div(
            style = "border: 2px dashed #3498db; border-radius: 10px; padding: 40px; text-align: center; margin-bottom: 20px;",
            fileInput(
              "data_file",
              NULL,
              accept = c(".csv", ".tsv", ".txt", ".xlsx", ".xls"),
              width = "100%",
              buttonLabel = "选择文件",
              placeholder = "拖拽文件到此处或点击选择"
            ),
            div(
              style = "margin-top: 15px; color: #7f8c8d;",
              icon("info-circle", style = "margin-right: 5px;"),
              "支持格式: CSV, TSV, TXT, Excel (.xlsx, .xls)"
            ),
            div(
              style = "color: #7f8c8d; font-size: 14px;",
              "最大文件大小: 100MB"
            )
          ),
          
          # 文件选项
          div(
            style = "background: #f8f9fa; padding: 20px; border-radius: 8px;",
            h4("文件选项", style = "margin-top: 0; color: #2c3e50;"),
            
            fluidRow(
              column(6,
                selectInput(
                  "file_separator",
                  "分隔符",
                  choices = list(
                    "逗号 (,)" = ",",
                    "制表符" = "\t",
                    "分号 (;)" = ";",
                    "空格" = " "
                  ),
                  selected = ","
                )
              ),
              column(6,
                selectInput(
                  "file_encoding",
                  "编码格式",
                  choices = list(
                    "UTF-8" = "UTF-8",
                    "GBK" = "GBK",
                    "GB2312" = "GB2312",
                    "Latin1" = "latin1"
                  ),
                  selected = "UTF-8"
                )
              )
            ),
            
            fluidRow(
              column(6,
                checkboxInput("file_header", "包含列名", value = TRUE)
              ),
              column(6,
                checkboxInput("file_stringsAsFactors", "字符串转因子", value = FALSE)
              )
            )
          ),
          
          
        )
      )
    ),
    
    # 上传状态
    column(4,
      box(
        title = "上传状态",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            id = "upload_status",
            style = "text-align: center; padding: 30px;",
            icon("cloud-upload-alt", style = "font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"),
            h4("等待上传", style = "color: #7f8c8d; margin: 0; font-weight: 300;"),
            p("请选择数据文件", style = "color: #95a5a6; margin: 10px 0 0 0;")
          ),
          
          # 文件信息（上传后显示）
          div(
            id = "file_info",
            style = "display: none;",
            
            h5("文件信息", style = "color: #2c3e50; margin-bottom: 15px;"),
            
            div(
              style = "background: #e8f5e8; padding: 10px; border-radius: 5px; margin-bottom: 10px;",
              strong("文件名: "), span(id = "file_name", "")
            ),
            
            div(
              style = "background: #e8f4fd; padding: 10px; border-radius: 5px; margin-bottom: 10px;",
              strong("文件大小: "), span(id = "file_size", "")
            ),
            
            div(
              style = "background: #fff3cd; padding: 10px; border-radius: 5px; margin-bottom: 10px;",
              strong("行数: "), span(id = "file_rows", "")
            ),
            
            div(
              style = "background: #f8d7da; padding: 10px; border-radius: 5px;",
              strong("列数: "), span(id = "file_cols", "")
            )
          )
        )
      ),
      
      # 示例数据
      box(
        title = "示例数据",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 15px;",
          p("您可以下载示例数据文件来测试系统功能：", style = "margin-bottom: 15px;"),
          
          downloadButton(
            "download_sample",
            "下载示例数据",
            icon = icon("download"),
            class = "btn-success btn-sm",
            style = "width: 100%;"
          ),
          
          div(
            style = "margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px; color: #6c757d;",
            "示例数据包含常见的医学研究变量，如年龄、性别、生化指标等。"
          )
        )
      )
    )
  )
)

# 数据预览界面
ui_data_preview <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("eye", style = "margin-right: 15px;"),
          "数据预览",
          style = "margin: 0; font-weight: 300;"
        ),
        p("查看和探索您的数据", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  # 数据概览
  fluidRow(
    column(3,
      valueBoxOutput("preview_rows", width = NULL)
    ),
    column(3,
      valueBoxOutput("preview_cols", width = NULL)
    ),
    column(3,
      valueBoxOutput("preview_missing", width = NULL)
    ),
    column(3,
      valueBoxOutput("preview_numeric", width = NULL)
    )
  ),
  
  # 数据表格
  fluidRow(
    column(12,
      box(
        title = tagList(
          icon("table"), 
          " 数据表格",
          tags$small(" - 显示数据集的前100行数据", 
                    style = "margin-left: 10px; color: #6c757d;")
        ),
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 10px;",
          DT::dataTableOutput("data_table")
        )
      )
    )
  ),
  
  # 变量信息
  fluidRow(
    column(12,
      box(
        title = tagList(
          icon("info-circle"), 
          " 变量信息",
          tags$small(" - 显示数据集中所有变量的类型、缺失值情况和统计描述", 
                    style = "margin-left: 10px; color: #6c757d;")
        ),
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 15px;",
          div(
            style = "margin-bottom: 15px; text-align: right;",
            downloadButton("download_variable_info_pdf", "下载PDF", 
                         class = "btn btn-primary btn-sm")
          ),
          div(
            style = "height: 400px; overflow-y: auto;",
            DT::dataTableOutput("variable_info", height = "350px")
          )
        )
      )
    )
  ),
  
  # 数据质量检查
  fluidRow(
    column(12,
      box(
        title = "数据质量检查",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          tabsetPanel(
            tabPanel(
              "缺失值分析",
              div(
                style = "padding: 20px;",
                div(
                  style = "margin-bottom: 15px; text-align: right;",
                  downloadButton("download_missing_pdf", "下载PDF", 
                               class = "btn btn-primary btn-sm")
                ),
                plotOutput("missing_plot", height = "400px")
              )
            ),
            
            tabPanel(
              "数据分布",
              div(
                style = "padding: 20px;",
                div(
                  style = "margin-bottom: 15px; text-align: right;",
                  downloadButton("download_distribution_pdf", "下载PDF", 
                               class = "btn btn-primary btn-sm")
                ),
                plotOutput("distribution_plot", height = "400px")
              )
            ),
            
            tabPanel(
              "相关性分析",
              div(
                style = "padding: 20px;",
                div(
                  style = "margin-bottom: 15px; text-align: right;",
                  downloadButton("download_correlation_pdf", "下载PDF", 
                               class = "btn btn-primary btn-sm")
                ),
                plotOutput("correlation_plot", height = "400px")
              )
            )
          )
        )
      )
    )
  )
)

# 数据清洗界面
ui_data_cleaning <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("broom", style = "margin-right: 15px;"),
          "数据清洗",
          style = "margin: 0; font-weight: 300;"
        ),
        p("清洗和预处理您的数据", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    # 清洗选项
    column(4,
      box(
        title = "清洗选项",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          h4("缺失值处理", style = "color: #2c3e50; margin-bottom: 15px;"),
          
          radioButtons(
            "missing_method",
            "处理方法",
            choices = list(
              "删除含缺失值的行" = "remove",
              "均值填充" = "mean",
              "中位数填充" = "median",
              "MICE多重插补" = "mice",
              "保持原样" = "none"
            ),
            selected = "mice"
          ),
          
          conditionalPanel(
            condition = "input.missing_method == 'mice'",
            numericInput("mice_iterations", "MICE迭代次数", value = 5, min = 1, max = 20)
          ),
          
          hr(),
          
          h4("变量转换", style = "color: #2c3e50; margin-bottom: 15px;"),
          
          checkboxInput("normalize_numeric", "标准化数值变量", value = FALSE),
          checkboxInput("log_transform", "对数变换", value = FALSE),
          checkboxInput("remove_outliers", "移除异常值", value = FALSE),
          
          conditionalPanel(
            condition = "input.remove_outliers",
            numericInput("outlier_threshold", "异常值阈值(IQR倍数)", value = 1.5, min = 1, max = 5, step = 0.1)
          ),
          
          hr(),
          
          div(
            style = "text-align: center;",
            actionButton(
              "clean_data",
              "开始清洗",
              icon = icon("play"),
              class = "btn-primary btn-lg",
              style = "width: 100%; padding: 12px;"
            )
          )
        )
      )
    ),
    
    # 清洗结果
    column(8,
      box(
        title = "清洗结果",
        status = "success",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            id = "cleaning_status",
            style = "text-align: center; padding: 50px;",
            icon("broom", style = "font-size: 48px; color: #bdc3c7; margin-bottom: 15px;"),
            h4("等待清洗", style = "color: #7f8c8d; margin: 0; font-weight: 300;"),
            p("请配置清洗选项并点击开始清洗", style = "color: #95a5a6; margin: 10px 0 0 0;")
          ),
          
          div(
            id = "cleaning_results",
            style = "display: none;",
            
            
            
            div(
              style = "margin-top: 20px;",

              tabsetPanel(
                tabPanel(
                  "清洗后数据",
                  div(
                    style = "padding: 15px;",
                    DT::dataTableOutput("cleaned_data_preview")
                  )
                ),

                tabPanel(
                  "清洗报告",
                  div(
                    style = "padding: 15px;",

                    h4("清洗统计", style = "color: #2c3e50; margin-bottom: 20px;"),

                    div(
                      id = "cleaning_report_content"
                    ),

                    div(
                      style = "margin-top: 20px; text-align: center;",
                      downloadButton(
                        "download_cleaned_data",
                        "下载清洗后数据",
                        icon = icon("download"),
                        class = "btn-success"
                      )
                    )
                  )
                )
              )
            )
          )
        )
      )
    )
  )
)

# 多文件上传与合并界面
ui_multi_file_upload <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
                 border-radius: 10px; color: white;",
        h2(
          icon("layer-group", style = "margin-right: 15px;"),
          "多文件上传与合并",
          style = "margin: 0; font-weight: 300;"
        ),
        p("支持拖拽多个文件或批量选择，通过icustay_id自动合并", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),

  fluidRow(
    # 文件上传区域
    column(8,
      box(
        title = "批量文件上传",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 20px;",

          # 批量文件上传区域
          div(
            style = "border: 2px dashed #3498db; border-radius: 10px; padding: 30px; text-align: center; margin-bottom: 20px; background: #f8f9fa;",

            fileInput(
              "multi_files_batch",
              NULL,
              accept = c(".csv", ".tsv", ".txt"),
              width = "100%",
              multiple = TRUE,
              buttonLabel = "选择多个文件",
              placeholder = "拖拽多个CSV文件到此处或点击选择"
            ),

            div(
              style = "margin-top: 15px; color: #7f8c8d;",
              icon("info-circle", style = "margin-right: 5px;"),
              "支持格式: CSV, TSV, TXT | 支持多文件选择和拖拽上传"
            ),
            div(
              style = "color: #7f8c8d; font-size: 14px;",
              "单个文件最大: 100MB | 建议同时上传不超过10个文件"
            )
          ),

          # 文件选项
          div(
            style = "background: #f8f9fa; padding: 20px; border-radius: 8px;",
            h4("文件选项", style = "margin-top: 0; color: #2c3e50;"),

            fluidRow(
              column(6,
                selectInput(
                  "batch_separator",
                  "分隔符",
                  choices = list(
                    "自动检测" = "auto",
                    "逗号 (,)" = ",",
                    "制表符" = "\t",
                    "分号 (;)" = ";",
                    "空格" = " "
                  ),
                  selected = "auto"
                )
              ),
              column(6,
                selectInput(
                  "batch_encoding",
                  "编码格式",
                  choices = list(
                    "UTF-8" = "UTF-8",
                    "GBK" = "GBK",
                    "GB2312" = "GB2312",
                    "Latin1" = "latin1"
                  ),
                  selected = "UTF-8"
                )
              )
            ),

            fluidRow(
              column(6,
                checkboxInput("batch_header", "包含列名", value = TRUE)
              ),
              column(6,
                checkboxInput("batch_auto_desc", "自动生成描述", value = TRUE)
              )
            )
          )
        )
      )
    ),

    # 合并配置
    column(4,
      box(
        title = "合并配置",
        status = "info",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 20px;",

          selectInput(
            "merge_key",
            "合并键",
            choices = list("icustay_id" = "icustay_id"),
            selected = "icustay_id"
          ),

          selectInput(
            "merge_type",
            "合并类型",
            choices = list(
              "内连接 (只保留共同记录)" = "inner",
              "左连接 (以第一个文件为主)" = "left",
              "外连接 (保留所有记录)" = "outer"
            ),
            selected = "inner"
          ),

          checkboxInput("remove_duplicates", "移除重复列", value = TRUE),
          checkboxInput("add_source", "添加来源信息", value = FALSE),

          hr(),

          div(
            style = "text-align: center;",
            actionButton(
              "merge_files",
              "开始合并",
              icon = icon("play"),
              class = "btn-success btn-lg",
              style = "width: 100%; padding: 12px;"
            )
          )
        )
      )
    )
  ),

  # 文件状态表格
  fluidRow(
    column(12,
      box(
        title = "文件状态",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,

        div(
          style = "padding: 15px;",
          DT::dataTableOutput("file_status_table")
        )
      )
    )
  ),

  # 合并结果展示
  fluidRow(
    column(12,
      div(
        id = "merge_results_section",
        style = "display: none;",

        box(
          title = "合并结果",
          status = "success",
          solidHeader = TRUE,
          width = NULL,

          div(
            style = "padding: 20px;",

            tabsetPanel(
              tabPanel(
                "数据预览",
                div(
                  style = "padding: 15px;",
                  DT::dataTableOutput("merged_data_preview")
                )
              ),

              tabPanel(
                "合并报告",
                div(
                  style = "padding: 15px;",

                  h4("合并统计", style = "color: #2c3e50; margin-bottom: 20px;"),

                  div(
                    id = "merge_report_content"
                  ),

                  div(
                    style = "margin-top: 20px; text-align: center;",
                    downloadButton(
                      "download_merged_data",
                      "下载合并数据",
                      icon = icon("download"),
                      class = "btn-success"
                    )
                  )
                )
              ),

              tabPanel(
                "数据质量",
                div(
                  style = "padding: 15px;",
                  plotOutput("merged_data_quality", height = "400px")
                )
              )
            )
          )
        )
      )
    )
  )
)
