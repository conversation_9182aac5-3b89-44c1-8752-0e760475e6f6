# LASSO回归分析使用指南

## 概述

LASSO（Least Absolute Shrinkage and Selection Operator）回归是一种正则化回归方法，通过L1惩罚项实现变量选择和系数压缩。在医学数据分析中，LASSO回归特别适用于：

- 高维数据的变量选择
- 多重共线性问题的处理
- 构建稀疏预测模型
- 避免过拟合

## 功能特性

### 1. 支持的分析类型
- **二分类结局**：使用`family = "binomial"`
- **连续结局**：使用`family = "gaussian"`
- **生存分析**：使用`family = "cox"`（需要survival包）

### 2. 参数设置
- **Alpha (α)**：控制L1惩罚强度
  - α = 1：纯LASSO回归
  - α = 0：纯Ridge回归
  - 0 < α < 1：Elastic Net
- **交叉验证折数**：默认5折，可调整
- **Lambda选择策略**：
  - `lambda.min`：最小交叉验证误差
  - `lambda.1se`：最小交叉验证误差+1个标准误差（推荐）

### 3. 数据预处理
- 自动处理分类变量（创建虚拟变量）
- 数值变量标准化
- 缺失值处理（完整案例分析）
- 数据验证和错误检查

## 使用方法

### 1. 基本使用

```r
# 基本LASSO分析
lasso_results <- perform_lasso_analysis(
  data = your_data,
  outcome_var = "outcome",
  alpha = 1,
  cv_folds = 5,
  lambda_selection = "lambda.1se",
  family = "binomial"
)
```

### 2. 指定协变量

```r
# 指定特定协变量
selected_covariates <- c("age", "bmi", "gender", "smoking")
lasso_results <- perform_lasso_analysis(
  data = your_data,
  outcome_var = "outcome",
  covariates = selected_covariates,
  alpha = 1,
  cv_folds = 5
)
```

### 3. 不同结局类型

```r
# 连续结局变量
lasso_results_continuous <- perform_lasso_analysis(
  data = your_data,
  outcome_var = "continuous_outcome",
  family = "gaussian"
)

# 生存分析
lasso_results_survival <- perform_lasso_analysis(
  data = your_data,
  outcome_var = cbind(time, status),
  family = "cox"
)
```

## 结果解释

### 1. 主要输出

```r
lasso_results <- list(
  # 模型对象
  model = lasso_model,           # glmnet模型
  cv_model = cv_lasso,          # 交叉验证结果
  
  # Lambda值
  optimal_lambda = optimal_lambda,  # 最优lambda
  lambda_min = lambda_min,          # lambda.min
  lambda_1se = lambda_1se,         # lambda.1se
  
  # 变量选择结果
  selected_variables = selected_vars,  # 选择的变量
  n_selected = length(selected_vars), # 选择变量数量
  
  # 系数表
  coefficients = coef_table,         # 系数详情
  
  # 模型性能
  model_metrics = model_metrics,     # 性能指标
  
  # 数据信息
  data_dimensions = list(...)        # 数据维度信息
)
```

### 2. 系数表结构

| 列名 | 说明 |
|------|------|
| Variable | 变量名 |
| Coefficient | 回归系数 |
| Variable_Type | 变量类型（Numeric/Categorical/Intercept） |

### 3. 性能指标

**二分类结局：**
- AUC：曲线下面积
- 准确率：分类正确比例
- 敏感性：真阳性率
- 特异性：真阴性率
- PPV：阳性预测值
- NPV：阴性预测值

**连续结局：**
- MSE：均方误差
- RMSE：均方根误差
- MAE：平均绝对误差
- R²：决定系数

## 可视化功能

### 1. 系数路径图
```r
# 绘制系数路径图
path_plot <- create_lasso_plot(lasso_results, "LASSO系数路径图")
```

### 2. 交叉验证图
```r
# 绘制交叉验证图
cv_plot <- create_lasso_cv_plot(lasso_results, "LASSO交叉验证图")
```

### 3. 系数条形图
```r
# 绘制系数条形图
coef_plot <- create_lasso_coefficient_plot(lasso_results, "LASSO回归系数")
```

## 最佳实践

### 1. 数据准备
- 确保结局变量编码正确（二分类：0/1）
- 检查分类变量的水平数（至少2个）
- 处理极端值和异常值
- 考虑变量之间的相关性

### 2. 参数选择
- 从`alpha = 1`开始（纯LASSO）
- 使用`lambda.1se`获得更稳定的模型
- 交叉验证折数建议5-10折
- 样本量较小时减少交叉验证折数

### 3. 结果解释
- 关注选择的变量数量
- 比较不同alpha值的结果
- 验证模型性能指标
- 考虑临床意义

### 4. 模型验证
- 使用独立验证集
- 进行Bootstrap验证
- 检查模型稳定性
- 评估预测性能

## 常见问题

### 1. 没有选择任何变量
**可能原因：**
- Lambda值过大
- 数据相关性过高
- 样本量不足

**解决方案：**
- 减小alpha值
- 检查数据相关性
- 增加样本量

### 2. 选择了过多变量
**可能原因：**
- Lambda值过小
- 数据噪声过多

**解决方案：**
- 增大alpha值
- 使用`lambda.1se`而非`lambda.min`
- 数据预处理和清洗

### 3. 模型性能不佳
**可能原因：**
- 变量选择不当
- 数据质量问题
- 模型假设不满足

**解决方案：**
- 检查变量重要性
- 改进数据质量
- 考虑其他建模方法

## 技术细节

### 1. 算法实现
- 基于`glmnet`包
- 使用坐标下降算法
- 支持稀疏矩阵运算

### 2. 计算复杂度
- 时间复杂度：O(n×p×iterations)
- 空间复杂度：O(n×p)
- 其中n为样本数，p为变量数

### 3. 内存要求
- 建议内存：至少4GB
- 大数据集：考虑分批处理
- 高维数据：使用稀疏矩阵

## 扩展功能

### 1. 集成学习
- 结合多个LASSO模型
- Bootstrap聚合
- 稳定性选择

### 2. 多任务学习
- 多结局变量建模
- 共享变量选择
- 组LASSO

### 3. 时间序列
- 动态LASSO
- 在线学习
- 变化点检测

## 参考文献

1. Tibshirani, R. (1996). Regression shrinkage and selection via the lasso. *Journal of the Royal Statistical Society: Series B*, 58(1), 267-288.

2. Friedman, J., Hastie, T., & Tibshirani, R. (2010). Regularization paths for generalized linear models via coordinate descent. *Journal of statistical software*, 33(1), 1-22.

3. Hastie, T., Tibshirani, R., & Wainwright, M. (2015). *Statistical learning with sparsity: the lasso and generalizations*. CRC press.

## 技术支持

如遇到问题，请检查：
1. 数据格式是否正确
2. 必要包是否已安装
3. 错误日志信息
4. 系统资源是否充足

更多技术支持请联系开发团队。 