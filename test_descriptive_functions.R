# 测试描述性统计功能
# Test Descriptive Statistics Functions

# 加载必要的库
library(tableone)
library(car)

# 设置工作目录
setwd("medical_analysis_system")

# 加载函数
source("modules/statistical_analysis.R")
source("utils/helpers.R")

# 生成测试数据
cat("生成测试数据...\n")
test_data <- generate_sample_data(100, seed = 123)

# 添加分组变量
test_data$group <- sample(c("Group_A", "Group_B"), nrow(test_data), replace = TRUE)

cat("测试数据维度:", dim(test_data), "\n")
cat("数值变量:", sum(sapply(test_data, is.numeric)), "\n")
cat("分类变量:", sum(sapply(test_data, function(x) is.character(x) || is.factor(x))), "\n")

# 测试1: 基本描述性统计
cat("\n=== 测试1: 基本描述性统计 ===\n")
tryCatch({
  result1 <- perform_descriptive_analysis(test_data, show_missing = FALSE, 
                                         show_normal_test = FALSE, 
                                         show_variance_test = FALSE)
  cat("✓ 基本描述性统计测试通过\n")
  cat("输出组件:", names(result1), "\n")
}, error = function(e) {
  cat("✗ 基本描述性统计测试失败:", e$message, "\n")
})

# 测试2: 缺失值分析
cat("\n=== 测试2: 缺失值分析 ===\n")
tryCatch({
  result2 <- perform_descriptive_analysis(test_data, show_missing = TRUE, 
                                         show_normal_test = FALSE, 
                                         show_variance_test = FALSE)
  if (!is.null(result2$missing_analysis)) {
    cat("✓ 缺失值分析测试通过\n")
    cat("缺失值统计行数:", nrow(result2$missing_analysis$missing_stats), "\n")
    cat("总体缺失率:", result2$missing_analysis$summary_stats$overall_missing_percent, "%\n")
  } else {
    cat("✗ 缺失值分析结果为空\n")
  }
}, error = function(e) {
  cat("✗ 缺失值分析测试失败:", e$message, "\n")
})

# 测试3: 正态性检验
cat("\n=== 测试3: 正态性检验 ===\n")
tryCatch({
  result3 <- perform_descriptive_analysis(test_data, show_missing = FALSE, 
                                         show_normal_test = TRUE, 
                                         show_variance_test = FALSE)
  if (!is.null(result3$normality_tests)) {
    cat("✓ 正态性检验测试通过\n")
    cat("检验结果行数:", nrow(result3$normality_tests), "\n")
    cat("检验的变量数:", length(unique(result3$normality_tests$Variable)), "\n")
  } else {
    cat("✗ 正态性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 正态性检验测试失败:", e$message, "\n")
})

# 测试4: 方差齐性检验
cat("\n=== 测试4: 方差齐性检验 ===\n")
tryCatch({
  result4 <- perform_descriptive_analysis(test_data, group_var = "group",
                                         show_missing = FALSE, 
                                         show_normal_test = FALSE, 
                                         show_variance_test = TRUE)
  if (!is.null(result4$variance_tests)) {
    cat("✓ 方差齐性检验测试通过\n")
    cat("检验结果行数:", nrow(result4$variance_tests), "\n")
    cat("检验的变量数:", length(unique(result4$variance_tests$Variable)), "\n")
  } else {
    cat("✗ 方差齐性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 方差齐性检验测试失败:", e$message, "\n")
})

# 测试5: 完整功能测试
cat("\n=== 测试5: 完整功能测试 ===\n")
tryCatch({
  result5 <- perform_descriptive_analysis(test_data, group_var = "group",
                                         show_missing = TRUE, 
                                         show_normal_test = TRUE, 
                                         show_variance_test = TRUE)
  
  components <- c("table_one", "table_one_df", "numeric_vars", "categorical_vars",
                  "missing_analysis", "normality_tests", "variance_tests")
  
  available_components <- names(result5)
  cat("可用组件:", paste(available_components, collapse = ", "), "\n")
  
  missing_components <- setdiff(components, available_components)
  if (length(missing_components) == 0) {
    cat("✓ 完整功能测试通过 - 所有组件都可用\n")
  } else {
    cat("⚠ 部分组件缺失:", paste(missing_components, collapse = ", "), "\n")
  }
  
}, error = function(e) {
  cat("✗ 完整功能测试失败:", e$message, "\n")
})

cat("\n=== 测试完成 ===\n")
