// 使用Perfect Scrollbar插件修复DataTables滚动问题
$(document).ready(function() {
  
  console.log('Perfect Scrollbar修复脚本开始加载...');
  
  // 动态加载Perfect Scrollbar CSS和JS
  function loadPerfectScrollbar() {
    // 检查是否已经加载
    if (typeof PerfectScrollbar !== 'undefined') {
      console.log('Perfect Scrollbar已加载');
      return Promise.resolve();
    }
    
    // 加载CSS
    if (!$('link[href*="perfect-scrollbar"]').length) {
      $('<link>')
        .attr('rel', 'stylesheet')
        .attr('href', 'https://cdn.jsdelivr.net/npm/perfect-scrollbar@1.5.5/css/perfect-scrollbar.min.css')
        .appendTo('head');
    }
    
    // 加载JS
    return new Promise((resolve, reject) => {
      if (typeof PerfectScrollbar !== 'undefined') {
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/perfect-scrollbar@1.5.5/dist/perfect-scrollbar.min.js';
      script.onload = () => {
        console.log('Perfect Scrollbar JS加载完成');
        resolve();
      };
      script.onerror = () => {
        console.error('Perfect Scrollbar JS加载失败');
        reject();
      };
      document.head.appendChild(script);
    });
  }
  
  // 修复DataTables滚动问题
  function fixDataTablesWithPerfectScrollbar() {
    console.log('开始使用Perfect Scrollbar修复DataTables...');
    
    $('.dataTables_wrapper').each(function() {
      const $wrapper = $(this);
      const $scrollBody = $wrapper.find('.dataTables_scrollBody');
      const $scrollHead = $wrapper.find('.dataTables_scrollHead');
      
      if ($scrollBody.length && !$scrollBody.hasClass('ps-fixed')) {
        console.log('修复表格滚动体:', $scrollBody.length);
        
        // 移除原有的滚动样式
        $scrollBody.css({
          'overflow': 'hidden',
          'position': 'relative'
        });
        
        // 应用Perfect Scrollbar
        try {
          const ps = new PerfectScrollbar($scrollBody[0], {
            wheelPropagation: false,
            suppressScrollX: false,
            suppressScrollY: false
          });
          
          // 标记已修复
          $scrollBody.addClass('ps-fixed').data('ps', ps);
          
          // 监听表格重绘事件，重新应用滚动条
          $wrapper.on('draw.dt', function() {
            setTimeout(() => {
              if (ps) {
                ps.update();
              }
            }, 100);
          });
          
          // 监听窗口大小变化
          $(window).on('resize', function() {
            if (ps) {
              ps.update();
            }
          });
          
        } catch (e) {
          console.error('应用Perfect Scrollbar失败:', e);
        }
      }
      
      // 修复表格头部
      if ($scrollHead.length && !$scrollHead.hasClass('ps-fixed')) {
        console.log('修复表格头部:', $scrollHead.length);
        
        $scrollHead.css({
          'overflow': 'hidden',
          'position': 'relative'
        });
        
        try {
          const psHead = new PerfectScrollbar($scrollHead[0], {
            wheelPropagation: false,
            suppressScrollX: false,
            suppressScrollY: true
          });
          
          $scrollHead.addClass('ps-fixed').data('ps', psHead);
          
        } catch (e) {
          console.error('应用Perfect Scrollbar到头部失败:', e);
        }
      }
    });
  }
  
  // 确保控制元素始终可点击
  function ensureControlsClickable() {
    $('.dataTables_wrapper').each(function() {
      const $wrapper = $(this);
      
      // 修复过滤器
      $wrapper.find('.dataTables_filter').css({
        'position': 'relative',
        'z-index': '9998',
        'background': 'white'
      });
      
      $wrapper.find('.dataTables_filter input').css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'pointer-events': 'auto'
      });
      
      // 修复长度选择器
      $wrapper.find('.dataTables_length').css({
        'position': 'relative',
        'z-index': '9998',
        'background': 'white'
      });
      
      $wrapper.find('.dataTables_length select').css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'pointer-events': 'auto'
      });
      
      // 修复分页按钮
      $wrapper.find('.dataTables_paginate').css({
        'position': 'relative',
        'z-index': '9998',
        'background': 'white'
      });
      
      $wrapper.find('.dataTables_paginate .paginate_button').css({
        'position': 'relative',
        'z-index': '9999',
        'background': 'white',
        'pointer-events': 'auto'
      });
      
      // 修复导出按钮
      $wrapper.find('.dt-buttons').css({
        'position': 'relative',
        'z-index': '9998',
        'background': 'white'
      });
      
      $wrapper.find('.dt-buttons .dt-button').css({
        'position': 'relative',
        'z-index': '9999',
        'pointer-events': 'auto'
      });
    });
  }
  
  // 主修复函数
  function mainFix() {
    ensureControlsClickable();
    fixDataTablesWithPerfectScrollbar();
  }
  
  // 监听DataTables事件
  $(document).on('init.dt', function(e, settings) {
    console.log('DataTables初始化事件触发');
    setTimeout(mainFix, 200);
  });
  
  $(document).on('draw.dt', function(e, settings) {
    console.log('DataTables重绘事件触发');
    setTimeout(mainFix, 100);
  });
  
  $(document).on('page.dt', function(e, settings) {
    console.log('DataTables分页事件触发');
    setTimeout(mainFix, 100);
  });
  
  $(document).on('search.dt', function(e, settings) {
    console.log('DataTables搜索事件触发');
    setTimeout(mainFix, 100);
  });
  
  // 监听DOM变化
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1 && $(node).find('.dataTables_wrapper').length > 0) {
            console.log('检测到新的DataTables元素');
            setTimeout(mainFix, 200);
          }
        });
      }
    });
  });
  
  // 开始观察DOM变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 监听窗口大小变化
  $(window).resize(function() {
    console.log('窗口大小变化，重新修复');
    setTimeout(mainFix, 100);
  });
  
  // 初始化
  loadPerfectScrollbar().then(() => {
    console.log('Perfect Scrollbar加载完成，开始修复');
    setTimeout(mainFix, 500);
  }).catch(() => {
    console.log('使用备用修复方案');
    setTimeout(mainFix, 500);
  });
  
  // 定期检查
  setInterval(function() {
    if ($('.dataTables_wrapper').length > 0) {
      mainFix();
    }
  }, 3000);
  
  console.log('Perfect Scrollbar修复脚本加载完成');
}); 