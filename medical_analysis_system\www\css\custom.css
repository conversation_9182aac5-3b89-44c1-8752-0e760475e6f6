/* 医学数据分析系统 - 自定义样式 */
/* Medical Data Analysis System - Custom CSS */

/* 全局样式 */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* 页面头部样式 */
.page-header {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.page-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片样式增强 */
.box {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
}

.box:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.box-header {
  border-radius: 10px 10px 0 0;
}

.box-body {
  border-radius: 0 0 10px 10px;
}

/* 按钮样式增强 */
.btn {
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.btn-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.btn-danger {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 值框样式 */
.small-box {
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.small-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.small-box .icon {
  transition: all 0.3s ease;
}

.small-box:hover .icon {
  transform: scale(1.1);
}

/* 表格样式 */
.dataTables_wrapper {
  margin-top: 20px;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
  margin: 10px 0;
}

.table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table thead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-weight: 600;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* 文件上传区域 */
.file-upload-area {
  border: 2px dashed #007bff;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9ff 0%, #e6f3ff 100%);
}

.file-upload-area:hover {
  border-color: #0056b3;
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
  transform: scale(1.02);
}

.file-upload-area.dragover {
  border-color: #28a745;
  background: linear-gradient(135deg, #f0fff4 0%, #d4edda 100%);
}

/* 进度条样式 */
.progress {
  height: 8px;
  border-radius: 10px;
  background-color: #e9ecef;
  overflow: hidden;
}

.progress-bar {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transition: width 0.6s ease;
}

/* 标签页样式 */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
  border: none;
  border-radius: 25px 25px 0 0;
  margin-right: 5px;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* 表单控件样式 */
.form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  transform: scale(1.02);
}

.form-group label {
  font-weight: 600;
  color: #495057;
}

/* 选择框样式 */
.selectize-input {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.selectize-input.focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 复选框和单选框样式 */
.checkbox input[type="checkbox"],
.radio input[type="radio"] {
  transform: scale(1.2);
  margin-right: 8px;
}

/* 警告和提示样式 */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.alert-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
}

.alert-info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
}

/* 加载动画 */
.spinner-border {
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

/* 图表容器样式 */
.plot-container {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
}

/* 侧边栏样式增强 */
.main-sidebar {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-menu > li > a {
  transition: all 0.3s ease;
}

.sidebar-menu > li > a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .page-header h1,
  .page-header h2 {
    font-size: 1.5rem;
  }
  
  .box {
    margin-bottom: 20px;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .small-box {
    margin-bottom: 20px;
  }
}

/* 打印样式 */
@media print {
  .main-sidebar,
  .main-header,
  .btn,
  .no-print {
    display: none !important;
  }
  
  .content-wrapper {
    margin-left: 0 !important;
  }
  
  .box {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* DataTables 滚动问题修复 */
.dataTables_wrapper {
  position: relative;
}

.dataTables_filter,
.dataTables_length,
.dataTables_info,
.dataTables_paginate {
  position: relative;
  z-index: 1000;
}

/* 确保过滤器输入框始终可点击 */
.dataTables_filter input {
  position: relative;
  z-index: 1001;
  background: white;
}

/* 修复水平滚动时的z-index问题 */
.dataTables_scrollHead {
  position: relative;
  z-index: 999;
}

.dataTables_scrollBody {
  position: relative;
  z-index: 1;
}

/* 确保表格头部过滤器不被遮挡 */
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 修复表格容器的overflow问题 */
.dataTables_scroll {
  overflow: visible !important;
}

.dataTables_scrollBody {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 确保按钮组不被遮挡 */
.dt-buttons {
  position: relative;
  z-index: 1000;
  margin-bottom: 10px;
}

/* 修复表格行选择器的问题 */
.dataTables_wrapper .dataTables_length select {
  position: relative;
  z-index: 1001;
}

/* 确保分页控件可点击 */
.dataTables_paginate .paginate_button {
  position: relative;
  z-index: 1001;
}

/* 修复表格行悬停效果 */
.dataTables_wrapper .dataTable tbody tr:hover {
  background-color: #f8f9fa !important;
  cursor: pointer;
}

/* 确保表格内容不被截断 */
.dataTables_wrapper .dataTable {
  width: 100% !important;
}

/* 修复表格列宽问题 */
.dataTables_wrapper .dataTable th,
.dataTables_wrapper .dataTable td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 更全面的DataTables滚动问题修复 */
.dataTables_wrapper {
  position: relative !important;
  z-index: 1;
}

/* 确保所有控制元素都在最上层 */
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  position: relative !important;
  z-index: 1000 !important;
  background: white;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
}

/* 过滤器输入框样式 */
.dataTables_wrapper .dataTables_filter input {
  position: relative !important;
  z-index: 1001 !important;
  background: white !important;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 200px;
}

.dataTables_wrapper .dataTables_filter input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

/* 表格长度选择器 */
.dataTables_wrapper .dataTables_length select {
  position: relative !important;
  z-index: 1001 !important;
  background: white !important;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
}

/* 分页按钮 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  position: relative !important;
  z-index: 1001 !important;
  background: white !important;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  margin: 0 2px;
  cursor: pointer;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #e9ecef !important;
  border-color: #adb5bd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: #667eea !important;
  color: white !important;
  border-color: #667eea;
}

/* 按钮组样式 */
.dt-buttons {
  position: relative !important;
  z-index: 1000 !important;
  margin-bottom: 10px;
}

.dt-buttons .dt-button {
  position: relative !important;
  z-index: 1001 !important;
  background: #667eea !important;
  color: white !important;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 5px;
  cursor: pointer;
}

.dt-buttons .dt-button:hover {
  background: #5a6fd8 !important;
}

/* 表格头部固定 */
.dataTables_scrollHead {
  position: relative !important;
  z-index: 999 !important;
  background: white;
}

.dataTables_scrollHead table {
  border-collapse: collapse;
}

.dataTables_scrollHead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none;
  font-weight: 600;
  padding: 12px 8px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 998;
}

/* 表格主体滚动 */
.dataTables_scrollBody {
  position: relative !important;
  z-index: 1 !important;
  overflow-x: auto !important;
  overflow-y: auto !important;
  max-height: 400px;
}

.dataTables_scrollBody table {
  border-collapse: collapse;
}

.dataTables_scrollBody td {
  padding: 8px;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dataTables_scrollBody tr:hover {
  background-color: #f8f9fa !important;
}

/* 表格行选择 */
.dataTables_scrollBody tr.selected {
  background-color: #e3f2fd !important;
}

/* 修复表格容器overflow问题 */
.dataTables_scroll {
  overflow: visible !important;
}

/* 确保表格内容不被截断 */
.dataTables_wrapper .dataTable {
  width: 100% !important;
  table-layout: auto;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .dataTables_wrapper .dataTables_filter input {
    min-width: 150px;
  }
  
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_info {
    text-align: center;
    margin-bottom: 10px;
  }
  
  .dt-buttons {
    text-align: center;
  }
  
  .dt-buttons .dt-button {
    margin-bottom: 5px;
  }
}

/* 修复表格列宽问题 */
.dataTables_wrapper .dataTable th,
.dataTables_wrapper .dataTable td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 确保过滤器区域始终可见 */
.dataTables_wrapper .dataTables_filter {
  position: sticky !important;
  top: 0;
  z-index: 1002 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #dee2e6;
  margin: 0;
  padding: 15px;
  border-radius: 0;
}

/* 修复表格排序图标 */
.dataTables_wrapper .dataTable th.sorting,
.dataTables_wrapper .dataTable th.sorting_asc,
.dataTables_wrapper .dataTable th.sorting_desc {
  position: relative;
  cursor: pointer;
}

.dataTables_wrapper .dataTable th.sorting:after,
.dataTables_wrapper .dataTable th.sorting_asc:after,
.dataTables_wrapper .dataTable th.sorting_desc:after {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.dataTables_wrapper .dataTable th.sorting:after {
  content: "\f0dc";
  color: #ccc;
}

.dataTables_wrapper .dataTable th.sorting_asc:after {
  content: "\f0de";
  color: #667eea;
}

.dataTables_wrapper .dataTable th.sorting_desc:after {
  content: "\f0dd";
  color: #667eea;
}

/* 简化DataTables样式 - 配合Perfect Scrollbar使用 */
.dataTables_wrapper {
  position: relative;
}

/* 基本控制元素样式 */
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  position: relative;
  z-index: 1000;
  background: white;
  padding: 10px;
  margin-bottom: 10px;
}

/* 过滤器输入框样式 */
.dataTables_wrapper .dataTables_filter input {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 200px;
}

/* 表格长度选择器样式 */
.dataTables_wrapper .dataTables_length select {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
}

/* 分页按钮强制样式 */
.dataTables_wrapper .dataTables_paginate .paginate_button,
.dataTables_wrapper .dataTables_paginate .paginate_button:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button:focus {
  position: relative !important;
  z-index: 10000 !important;
  background: white !important;
  border: 2px solid #dee2e6 !important;
  border-radius: 6px !important;
  padding: 8px 15px !important;
  margin: 0 3px !important;
  cursor: pointer !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #f8f9fa !important;
  border-color: #667eea !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: #667eea !important;
  color: white !important;
  border-color: #667eea !important;
}

/* 按钮组强制样式 */
.dt-buttons,
.dt-buttons * {
  position: relative !important;
  z-index: 9999 !important;
}

.dt-buttons .dt-button,
.dt-buttons .dt-button:hover,
.dt-buttons .dt-button:focus {
  position: relative !important;
  z-index: 10000 !important;
  background: #667eea !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 8px 15px !important;
  margin-right: 8px !important;
  cursor: pointer !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.dt-buttons .dt-button:hover {
  background: #5a6fd8 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 表格头部强制样式 */
.dataTables_scrollHead,
.dataTables_scrollHead * {
  position: relative !important;
  z-index: 999 !important;
  background: white !important;
}

.dataTables_scrollHead th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 700 !important;
  padding: 15px 10px !important;
  text-align: left !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 998 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 表格主体强制样式 */
.dataTables_scrollBody,
.dataTables_scrollBody * {
  position: relative !important;
  z-index: 1 !important;
  overflow-x: auto !important;
  overflow-y: auto !important;
}

.dataTables_scrollBody table {
  border-collapse: collapse !important;
  width: 100% !important;
}

.dataTables_scrollBody td {
  padding: 12px 10px !important;
  border-bottom: 1px solid #dee2e6 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  background: white !important;
}

.dataTables_scrollBody tr:hover td {
  background: #f8f9fa !important;
  transform: scale(1.01) !important;
  transition: all 0.2s ease !important;
}

/* 表格行选择强制样式 */
.dataTables_scrollBody tr.selected td {
  background: #e3f2fd !important;
  border-left: 4px solid #667eea !important;
}

/* 表格容器overflow强制修复 */
.dataTables_scroll {
  overflow: visible !important;
  position: relative !important;
}

/* 确保表格内容不被截断 */
.dataTables_wrapper .dataTable,
.dataTables_wrapper .dataTable * {
  width: 100% !important;
  table-layout: auto !important;
}

/* 表格列宽强制修复 */
.dataTables_wrapper .dataTable th,
.dataTables_wrapper .dataTable td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 250px !important;
  min-width: 100px !important;
}

/* 响应式表格强制修复 */
@media (max-width: 768px) {
  .dataTables_wrapper .dataTables_filter input {
    min-width: 200px !important;
    font-size: 16px !important;
  }
  
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_info {
    text-align: center !important;
    margin-bottom: 15px !important;
  }
  
  .dt-buttons {
    text-align: center !important;
  }
  
  .dt-buttons .dt-button {
    margin-bottom: 8px !important;
    width: 100% !important;
    text-align: center !important;
  }
  
  .dataTables_wrapper .dataTables_paginate {
    text-align: center !important;
  }
  
  .dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 3px !important;
    padding: 10px 12px !important;
  }
}

/* 表格排序图标强制修复 */
.dataTables_wrapper .dataTable th.sorting,
.dataTables_wrapper .dataTable th.sorting_asc,
.dataTables_wrapper .dataTable th.sorting_desc {
  position: relative !important;
  cursor: pointer !important;
  user-select: none !important;
}

.dataTables_wrapper .dataTable th.sorting:after,
.dataTables_wrapper .dataTable th.sorting_asc:after,
.dataTables_wrapper .dataTable th.sorting_desc:after {
  position: absolute !important;
  right: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  font-size: 14px !important;
}

.dataTables_wrapper .dataTable th.sorting:after {
  content: "\f0dc" !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

.dataTables_wrapper .dataTable th.sorting_asc:after {
  content: "\f0de" !important;
  color: white !important;
}

.dataTables_wrapper .dataTable th.sorting_desc:after {
  content: "\f0dd" !important;
  color: white !important;
}

/* 强制修复过滤器区域始终可见 */
.dataTables_wrapper .dataTables_filter {
  position: sticky !important;
  top: 0 !important;
  z-index: 10002 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border-bottom: 2px solid #dee2e6 !important;
  margin: 0 !important;
  padding: 20px !important;
  border-radius: 0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* 确保所有输入框和按钮在滚动时仍然可点击 */
.dataTables_wrapper .dataTables_filter input,
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_paginate .paginate_button,
.dt-buttons .dt-button {
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

/* 修复表格行点击事件 */
.dataTables_scrollBody tr {
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.dataTables_scrollBody tr:hover {
  background: #f8f9fa !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 强制修复表格头部固定 */
.dataTables_scrollHead {
  position: sticky !important;
  top: 0 !important;
  z-index: 999 !important;
  background: white !important;
}

/* 强制修复表格主体滚动 */
.dataTables_scrollBody {
  position: relative !important;
  z-index: 1 !important;
  max-height: 500px !important;
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 确保表格包装器有正确的定位 */
.dataTables_wrapper {
  position: relative !important;
  z-index: 1 !important;
  overflow: visible !important;
}
