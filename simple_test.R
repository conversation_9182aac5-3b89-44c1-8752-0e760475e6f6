# 简单测试方差齐性检验修复
# Simple Test for Variance Homogeneity Test Fix

# 设置工作目录
setwd("medical_analysis_system")

# 加载全局配置（包含logging函数）
source("global.R")

# 加载必要的库
suppressMessages({
  library(tableone)
  library(car)
})

# 创建简单的测试数据
cat("创建测试数据...\n")
set.seed(123)
test_data <- data.frame(
  age = rnorm(30, 65, 10),
  weight = rnorm(30, 70, 15),
  group = rep(c("A", "B"), 15),
  stringsAsFactors = FALSE
)

cat("测试数据创建完成，维度:", dim(test_data), "\n")
cat("变量名:", names(test_data), "\n")
cat("分组变量唯一值:", unique(test_data$group), "\n")

# 直接测试Levene检验
cat("\n=== 直接测试Levene检验 ===\n")
tryCatch({
  # 方法1: 使用公式语法
  cat("方法1: 使用公式语法\n")
  result1 <- leveneTest(age ~ group, data = test_data)
  cat("✓ 公式语法成功\n")
  print(result1)
  
  # 方法2: 直接传递向量
  cat("\n方法2: 直接传递向量\n")
  result2 <- leveneTest(test_data$age, as.factor(test_data$group))
  cat("✓ 向量传递成功\n")
  print(result2)
  
}, error = function(e) {
  cat("✗ Levene检验失败:", e$message, "\n")
})

# 直接测试Bartlett检验
cat("\n=== 直接测试Bartlett检验 ===\n")
tryCatch({
  # 方法1: 使用公式语法
  cat("方法1: 使用公式语法\n")
  result1 <- bartlett.test(age ~ group, data = test_data)
  cat("✓ 公式语法成功\n")
  print(result1)
  
  # 方法2: 直接传递向量
  cat("\n方法2: 直接传递向量\n")
  result2 <- bartlett.test(test_data$age, as.factor(test_data$group))
  cat("✓ 向量传递成功\n")
  print(result2)
  
}, error = function(e) {
  cat("✗ Bartlett检验失败:", e$message, "\n")
})

# 测试我们的方差齐性检验函数
cat("\n=== 测试方差齐性检验函数 ===\n")
tryCatch({
  numeric_vars <- c("age", "weight")
  result <- perform_variance_tests(test_data, numeric_vars, "group")
  
  if (!is.null(result) && nrow(result) > 0) {
    cat("✓ 方差齐性检验函数成功\n")
    print(result)
  } else {
    cat("✗ 方差齐性检验函数返回空结果\n")
  }
  
}, error = function(e) {
  cat("✗ 方差齐性检验函数失败:", e$message, "\n")
  cat("详细错误:", toString(e), "\n")
})

cat("\n=== 测试完成 ===\n")
