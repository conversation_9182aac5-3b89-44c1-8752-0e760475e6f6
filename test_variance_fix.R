# 测试方差齐性检验修复
# Test Variance Homogeneity Test Fix

# 设置工作目录
setwd("medical_analysis_system")

# 加载必要的库
suppressMessages({
  library(car)
  library(tableone)
})

# 加载函数
source("modules/statistical_analysis.R")
source("utils/helpers.R")

# 创建测试数据
cat("创建测试数据...\n")
test_data <- data.frame(
  patient_id = 1:100,
  age = rnorm(100, 65, 10),
  gender = sample(c("Male", "Female"), 100, replace = TRUE),
  creatinine = rnorm(100, 1.2, 0.3),
  group = sample(c("A", "B", "C"), 100, replace = TRUE),
  stringsAsFactors = FALSE
)

# 添加一些缺失值
test_data$creatinine[sample(1:100, 10)] <- NA

cat("测试数据创建完成\n")
cat("数据维度:", dim(test_data), "\n")
cat("列名:", paste(names(test_data), collapse = ", "), "\n")

# 测试方差齐性检验
cat("\n=== 测试方差齐性检验 ===\n")
tryCatch({
  numeric_vars <- c("age", "creatinine")
  group_var <- "group"
  
  cat("数值变量:", paste(numeric_vars, collapse = ", "), "\n")
  cat("分组变量:", group_var, "\n")
  
  result <- perform_variance_tests(test_data, numeric_vars, group_var)
  
  if (!is.null(result) && nrow(result) > 0) {
    cat("✓ 方差齐性检验成功\n")
    print(result)
  } else {
    cat("✗ 方差齐性检验结果为空\n")
  }
}, error = function(e) {
  cat("✗ 方差齐性检验失败:", e$message, "\n")
  cat("详细错误信息:", toString(e), "\n")
})

# 测试完整的描述性统计分析
cat("\n=== 测试完整描述性统计分析 ===\n")
tryCatch({
  result <- perform_descriptive_analysis(
    data = test_data,
    group_var = "group",
    show_missing = TRUE,
    show_normal_test = TRUE,
    show_variance_test = TRUE
  )
  
  cat("✓ 描述性统计分析成功\n")
  
  if (!is.null(result$variance_tests)) {
    cat("方差齐性检验结果:\n")
    print(result$variance_tests)
  }
  
  if (!is.null(result$normality_tests)) {
    cat("正态性检验结果:\n")
    print(result$normality_tests)
  }
  
}, error = function(e) {
  cat("✗ 描述性统计分析失败:", e$message, "\n")
  cat("详细错误信息:", toString(e), "\n")
})

cat("\n测试完成！\n") 