# 医学数据分析系统 - 主服务器逻辑
# Medical Data Analysis System - Main Server Logic

server <- function(input, output, session) {
  
  # 统一的响应式数据存储
  values <- reactiveValues(
    # 主数据存储
    raw_data = NULL,
    processed_data = NULL,
    analysis_results = list(),
    current_analysis_id = NULL,

    # 数据来源信息
    data_source = "none",  # "single_file", "multi_file", "none"
    data_info = list(),

    # 多文件相关数据
    multi_files = list(),
    file_counter = 1,
    merged_data = NULL,
    merge_report = NULL,
    cleaned_data = NULL,
    cleaning_report = NULL
  )
  
  # 仪表板相关输出
  output$data_status_box <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) "未上传" else "已上传",
      subtitle = "数据状态",
      icon = icon("database"),
      color = if (is.null(values$raw_data)) "red" else "green"
    )
  })
  
  output$analysis_tasks_box <- renderValueBox({
    valueBox(
      value = length(values$analysis_results),
      subtitle = "分析任务",
      icon = icon("tasks"),
      color = "blue"
    )
  })
  
  output$reports_count_box <- renderValueBox({
    valueBox(
      value = "0",
      subtitle = "生成报告",
      icon = icon("file-alt"),
      color = "yellow"
    )
  })
  
  output$system_uptime_box <- renderValueBox({
    valueBox(
      value = format(Sys.time(), "%H:%M"),
      subtitle = "系统时间",
      icon = icon("clock"),
      color = "purple"
    )
  })
  
  # 数据上传处理
  observeEvent(input$data_file, {
    req(input$data_file)
    
    tryCatch({
      # 显示加载提示
      showNotification("正在上传数据...", type = "message", duration = NULL, id = "upload_msg")
      
      # 读取数据
      file_path <- input$data_file$datapath
      
      values$raw_data <- read_medical_data(
        file_path,
        separator = input$file_separator,
        encoding = input$file_encoding,
        header = input$file_header,
        stringsAsFactors = input$file_stringsAsFactors
      )
      
      # 自动类型检测和转换
      values$raw_data <- detect_and_convert_types(values$raw_data)
      
      # 移除加载提示
      removeNotification("upload_msg")
      
      # 显示成功消息
      show_success(paste("数据上传成功！", nrow(values$raw_data), "行,", 
                        ncol(values$raw_data), "列"))
      
      # 更新变量选择列表
      updateSelectInput(session, "desc_group_var", 
                       choices = c("无" = "", get_variable_choices(values$raw_data)))
      
      updateCheckboxGroupInput(session, "desc_variables",
                              choices = get_variable_choices(values$raw_data))
      
      # 更新分析界面的变量选择
      binary_vars <- names(values$raw_data)[sapply(values$raw_data, function(x) {
        length(unique(x[!is.na(x)])) == 2
      })]
      
      updateSelectInput(session, "uni_outcome_var", choices = binary_vars)
      updateSelectInput(session, "multi_outcome_var", choices = binary_vars)
      updateSelectInput(session, "lasso_outcome_var", choices = binary_vars)
      
      all_vars <- get_variable_choices(values$raw_data)
      updateCheckboxGroupInput(session, "uni_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "multi_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "lasso_covariates", choices = all_vars)

      # 存储变量选择列表，供全选/反选功能使用
      values$desc_var_choices <- get_variable_choices(values$raw_data)
      values$analysis_var_choices <- all_vars
      
    }, error = function(e) {
      removeNotification("upload_msg")
      show_warning(paste("数据上传失败:", e$message))
    })
  })
  
  # 数据预览
  output$data_table <- DT::renderDataTable({
    req(values$raw_data)
    
    DT::datatable(
      values$raw_data,
      options = list(
        scrollX = TRUE,
        scrollY = '400px',
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel'),
        scrollCollapse = TRUE,
        autoWidth = FALSE,
        columnDefs = list(
          list(targets = "_all", width = 'auto')
        )
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE,
      extensions = c('Buttons')
    )
  })
  
  # 变量信息表
  output$variable_info <- DT::renderDataTable({
    req(values$raw_data)
    
    var_info <- create_variable_info_table(values$raw_data)
    
    if (is.null(var_info)) {
      return(DT::datatable(
        data.frame(Message = "无法生成变量信息表"),
        options = list(dom = 't'),
        class = "display compact",
        rownames = FALSE
      ))
    }
    
          DT::datatable(
        var_info,
        options = list(
          pageLength = 25,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel'),
          scrollX = TRUE,
          scrollY = '300px',
          scrollCollapse = TRUE,
          ordering = TRUE,
          autoWidth = FALSE,
          columnDefs = list(
            list(targets = 0, width = '60px'),   # Row列
            list(targets = 1, width = '180px'),  # Variable列
            list(targets = 2, width = '120px'),  # Type列
            list(targets = 3, width = '120px'),  # Missing_Count列
            list(targets = 4, width = '120px'),  # Missing_Percent列
            list(targets = 5, width = '120px'),  # Unique_Values列
            list(targets = 6, width = '400px')   # Description列
          )
        ),
        class = "display compact stripe",
        rownames = FALSE,
        caption = "变量信息概览",
        filter = 'top',
        extensions = c('Buttons', 'FixedColumns')
      ) %>%
      DT::formatStyle(
        'Missing_Percent',
        background = DT::styleInterval(
          c(5, 20, 50),
          c('white', '#fff3cd', '#f8d7da', '#d1ecf1')
        )
      ) %>%
      DT::formatStyle(
        'Type',
        background = DT::styleEqual(
          c('数值型', '字符型', '因子型', '逻辑型', '日期型'),
          c('#d4edda', '#d1ecf1', '#fff3cd', '#f8d7da', '#e2e3e5')
        )
      )
  })
  
  # 下载变量信息表PDF
  output$download_variable_info_pdf <- downloadHandler(
    filename = function() {
      paste0("变量信息表_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$raw_data)
        
        # 设置PDF参数
        pdf(file, width = 14, height = 10, family = "GB1")
        
        # 创建变量信息表
        var_info <- create_variable_info_table(values$raw_data)
        
        if (!is.null(var_info)) {
          # 使用grid.table绘制表格
          if (requireNamespace("gridExtra", quietly = TRUE)) {
            library(gridExtra)
            
            # 设置表格标题
            grid.newpage()
            grid.text("变量信息概览", x = 0.5, y = 0.95, 
                     gp = gpar(fontsize = 16, fontface = "bold"))
            
            # 绘制表格
            grid.table(var_info, 
                      rows = NULL,
                      theme = ttheme_default(
                        base_size = 10,
                        base_colour = "black",
                        base_family = "",
                        parse = FALSE,
                        padding = unit(c(4, 4), "mm")
                      ))
          } else {
            # 如果gridExtra不可用，使用基础绘图
            plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
            text(0.5, 0.8, "变量信息概览", cex = 1.5, font = 2)
            
            # 显示基本信息
            info_text <- paste(
              "总变量数:", ncol(values$raw_data),
              "\n总行数:", nrow(values$raw_data),
              "\n数值变量:", sum(sapply(values$raw_data, is.numeric)),
              "\n字符变量:", sum(sapply(values$raw_data, is.character)),
              "\n因子变量:", sum(sapply(values$raw_data, is.factor))
            )
            text(0.5, 0.5, info_text, cex = 1.2)
          }
        }
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 数据概览值框
  output$preview_rows <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else nrow(values$raw_data),
      subtitle = "数据行数",
      icon = icon("list"),
      color = "blue"
    )
  })
  
  output$preview_cols <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else ncol(values$raw_data),
      subtitle = "变量数量",
      icon = icon("columns"),
      color = "green"
    )
  })
  
  output$preview_missing <- renderValueBox({
    missing_count <- if (is.null(values$raw_data)) 0 else sum(is.na(values$raw_data))
    valueBox(
      value = missing_count,
      subtitle = "缺失值",
      icon = icon("question-circle"),
      color = if (missing_count > 0) "yellow" else "green"
    )
  })
  
  output$preview_numeric <- renderValueBox({
    numeric_count <- if (is.null(values$raw_data)) 0 else sum(sapply(values$raw_data, is.numeric))
    valueBox(
      value = numeric_count,
      subtitle = "数值变量",
      icon = icon("calculator"),
      color = "purple"
    )
  })
  
  # 缺失值模式图
  output$missing_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_missing_pattern_plot(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成缺失值图表", cex = 1.5, col = "red")
    })
  })
  
  # 下载缺失值分析图PDF
  output$download_missing_pdf <- downloadHandler(
    filename = function() {
      paste0("缺失值分析_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$raw_data)
        
        # 设置PDF参数
        pdf(file, width = 10, height = 8, family = "GB1")
        
        # 绘制图形
        missing_plot <- create_missing_pattern_plot(values$raw_data)
        
        if (!is.null(missing_plot)) {
          print(missing_plot)
        }
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 相关性热图
  output$correlation_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_correlation_heatmap(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成相关性图表", cex = 1.5, col = "red")
    })
  })
  
  # 下载相关性分析图PDF
  output$download_correlation_pdf <- downloadHandler(
    filename = function() {
      paste0("相关性分析_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$raw_data)
        
        # 设置PDF参数
        pdf(file, width = 10, height = 8, family = "GB1")
        
        # 绘制图形
        cor_plot <- create_correlation_heatmap(values$raw_data)
        
        if (!is.null(cor_plot)) {
          print(cor_plot)
        }
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 数据分布图
  output$distribution_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_distribution_plot(values$raw_data, title = "数据分布分析")
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("无法生成数据分布图:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 下载数据分布图PDF
  output$download_distribution_pdf <- downloadHandler(
    filename = function() {
      paste0("数据分布分析_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$raw_data)
        
        # 设置PDF参数
        pdf(file, width = 12, height = 10, family = "GB1")
        
        # 绘制图形
        dist_plot <- create_distribution_plot(values$raw_data, title = "数据分布分析")
        
        if (!is.null(dist_plot)) {
          if (inherits(dist_plot, "ggplot")) {
            print(dist_plot)
          } else {
            # 对于grid.arrange对象，直接打印即可
            print(dist_plot)
          }
        }
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 数据清洗
  observeEvent(input$clean_data, {
    req(values$raw_data)
    
    tryCatch({
      showNotification("正在清洗数据...", type = "message", duration = NULL, id = "clean_msg")
      
      # 准备清洗选项
      clean_options <- list(
        missing_method = input$missing_method,
        mice_iterations = input$mice_iterations,
        normalize = input$normalize_numeric,
        log_transform = input$log_transform,
        remove_outliers = input$remove_outliers,
        outlier_threshold = input$outlier_threshold
      )
      
      # 执行数据清洗
      values$processed_data <- preprocess_medical_data(values$raw_data, clean_options)

      # 生成清洗报告
      values$cleaning_report <- generate_cleaning_report(values$raw_data, values$processed_data, clean_options)

      # 更新变量选择（使用清洗后的数据）
      binary_vars <- names(values$processed_data)[sapply(values$processed_data, function(x) {
        length(unique(x[!is.na(x)])) == 2
      })]
      
      updateSelectInput(session, "uni_outcome_var", choices = binary_vars)
      updateSelectInput(session, "multi_outcome_var", choices = binary_vars)
      updateSelectInput(session, "lasso_outcome_var", choices = binary_vars)
      
      all_vars <- get_variable_choices(values$processed_data)
      updateCheckboxGroupInput(session, "uni_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "multi_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "lasso_covariates", choices = all_vars)

      # 更新存储的变量选择列表
      values$desc_var_choices <- get_variable_choices(values$processed_data)
      values$analysis_var_choices <- all_vars

      removeNotification("clean_msg")
      show_success("数据清洗完成！")

      # 显示清洗结果
      shinyjs::show("cleaning_results")
      shinyjs::hide("cleaning_status")
      
    }, error = function(e) {
      removeNotification("clean_msg")
      show_warning(paste("数据清洗失败:", e$message))
    })
  })

  # ========== 变量选择全选/反选功能 ==========

  # 描述性统计变量选择
  observeEvent(input$desc_select_all, {
    if (!is.null(values$desc_var_choices)) {
      updateCheckboxGroupInput(session, "desc_variables",
                              selected = as.character(values$desc_var_choices))
    }
  })

  observeEvent(input$desc_select_none, {
    if (!is.null(values$desc_var_choices)) {
      # 获取所有可选变量
      all_vars <- as.character(values$desc_var_choices)
      # 获取当前已选中的变量
      current_selected <- input$desc_variables
      # 计算反选：未选中的变为选中，已选中的变为未选中
      new_selected <- setdiff(all_vars, current_selected)
      updateCheckboxGroupInput(session, "desc_variables", selected = new_selected)
    }
  })

  # 单因素分析协变量选择
  observeEvent(input$uni_select_all, {
    if (!is.null(values$analysis_var_choices)) {
      updateCheckboxGroupInput(session, "uni_covariates",
                              selected = as.character(values$analysis_var_choices))
    }
  })

  observeEvent(input$uni_select_none, {
    if (!is.null(values$analysis_var_choices)) {
      # 获取所有可选变量
      all_vars <- as.character(values$analysis_var_choices)
      # 获取当前已选中的变量
      current_selected <- input$uni_covariates
      # 计算反选：未选中的变为选中，已选中的变为未选中
      new_selected <- setdiff(all_vars, current_selected)
      updateCheckboxGroupInput(session, "uni_covariates", selected = new_selected)
    }
  })

  # 多因素分析协变量选择
  observeEvent(input$multi_select_all, {
    if (!is.null(values$analysis_var_choices)) {
      updateCheckboxGroupInput(session, "multi_covariates",
                              selected = as.character(values$analysis_var_choices))
    }
  })

  observeEvent(input$multi_select_none, {
    if (!is.null(values$analysis_var_choices)) {
      # 获取所有可选变量
      all_vars <- as.character(values$analysis_var_choices)
      # 获取当前已选中的变量
      current_selected <- input$multi_covariates
      # 计算反选：未选中的变为选中，已选中的变为未选中
      new_selected <- setdiff(all_vars, current_selected)
      updateCheckboxGroupInput(session, "multi_covariates", selected = new_selected)
    }
  })

  # 更新多因素分析变量选择器
  observe({
    req(values$analysis_var_choices)
    
    # 更新结局变量选择器
    updateSelectInput(session, "multi_outcome_var", 
                     choices = values$analysis_var_choices,
                     selected = if (!is.null(input$multi_outcome_var) && 
                                   input$multi_outcome_var %in% values$analysis_var_choices) 
                                   input$multi_outcome_var else NULL)
    
    # 更新协变量选择器
    updateCheckboxGroupInput(session, "multi_covariates",
                           choices = values$analysis_var_choices,
                           selected = if (!is.null(input$multi_covariates)) 
                                       intersect(input$multi_covariates, values$analysis_var_choices) else NULL)
  })

  # LASSO分析候选变量选择
  observeEvent(input$lasso_select_all, {
    if (!is.null(values$analysis_var_choices)) {
      updateCheckboxGroupInput(session, "lasso_covariates",
                              selected = as.character(values$analysis_var_choices))
    }
  })

  observeEvent(input$lasso_select_none, {
    if (!is.null(values$analysis_var_choices)) {
      # 获取所有可选变量
      all_vars <- as.character(values$analysis_var_choices)
      # 获取当前已选中的变量
      current_selected <- input$lasso_covariates
      # 计算反选：未选中的变为选中，已选中的变为未选中
      new_selected <- setdiff(all_vars, current_selected)
      updateCheckboxGroupInput(session, "lasso_covariates", selected = new_selected)
    }
  })

  # 报告格式选择
  observeEvent(input$report_formats_select_all, {
    updateCheckboxGroupInput(session, "report_formats",
                            selected = c("html", "pdf", "docx"))
  })

  observeEvent(input$report_formats_select_none, {
    # 获取所有可选格式
    all_formats <- c("html", "pdf", "docx")
    # 获取当前已选中的格式
    current_selected <- input$report_formats
    # 计算反选：未选中的变为选中，已选中的变为未选中
    new_selected <- setdiff(all_formats, current_selected)
    updateCheckboxGroupInput(session, "report_formats", selected = new_selected)
  })

  # 清洗后数据预览
  output$cleaned_data_preview <- DT::renderDataTable({
    req(values$processed_data)

    DT::datatable(
      values$processed_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = "Bfrtip",
        buttons = c("copy", "csv", "excel")
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })

  # 清洗报告内容
  output$cleaning_report_content <- renderUI({
    req(values$cleaning_report)

    report <- values$cleaning_report

    # 检查报告是否有错误
    if (!is.null(report$error)) {
      return(div(
        style = "background: #f8d7da; padding: 20px; border-radius: 8px; color: #721c24;",
        h5("报告生成失败"),
        p(paste("错误信息:", report$error))
      ))
    }

    div(
      # 基本统计对比
      fluidRow(
        column(6,
          div(
            style = "background: #f8d7da; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            h5("清洗前", style = "margin: 0 0 15px 0; color: #721c24; font-weight: bold;"),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("数据行数:"), strong(format(report$original_rows, big.mark = ","))
            ),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("数据列数:"), strong(report$original_cols)
            ),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("缺失值总数:"), strong(format(report$original_missing_total, big.mark = ","))
            ),
            div(
              style = "display: flex; justify-content: space-between;",
              span("缺失值比例:"), strong(paste0(ifelse(is.null(report$original_missing_percent), "0", report$original_missing_percent), "%"))
            )
          )
        ),
        column(6,
          div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            h5("清洗后", style = "margin: 0 0 15px 0; color: #155724; font-weight: bold;"),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("数据行数:"), strong(format(report$final_rows, big.mark = ","))
            ),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("数据列数:"), strong(report$final_cols)
            ),
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("缺失值总数:"), strong(format(report$cleaned_missing_total, big.mark = ","))
            ),
            div(
              style = "display: flex; justify-content: space-between;",
              span("缺失值比例:"), strong(paste0(ifelse(is.null(report$cleaned_missing_percent), "0", report$cleaned_missing_percent), "%"))
            )
          )
        )
      ),

      # 处理效果统计
      if (!is.null(report$missing_reduction) && report$missing_reduction > 0) {
        div(
          style = "background: #d1ecf1; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #17a2b8;",
          h5("处理效果", style = "margin: 0 0 15px 0; color: #0c5460; font-weight: bold;"),
          div(
            style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
            span("减少缺失值:"), strong(format(report$missing_reduction, big.mark = ","))
          ),
          div(
            style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
            span("缺失值减少率:"), strong(paste0(report$missing_reduction_percent, "%"))
          ),
          if (report$removed_rows > 0) {
            div(
              style = "display: flex; justify-content: space-between; margin-bottom: 8px;",
              span("删除行数:"), strong(format(report$removed_rows, big.mark = ","))
            )
          },
          if (report$removed_cols > 0) {
            div(
              style = "display: flex; justify-content: space-between;",
              span("删除列数:"), strong(report$removed_cols)
            )
          }
        )
      },

      # 处理详情
      div(
        style = "background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
        h5("处理详情", style = "margin: 0 0 15px 0; color: #2c3e50; font-weight: bold;"),
        div(
          style = "margin-bottom: 10px;",
          strong("处理方法: "), span(ifelse(is.null(report$method_description), report$method, report$method_description), style = "color: #495057;")
        ),
        if (!is.null(report$normalize_applied) && report$normalize_applied) {
          div(
            style = "margin-bottom: 10px;",
            strong("数据标准化: "), span("已应用", style = "color: #28a745;"),
            if (length(report$normalized_variables) > 0) {
              span(paste0(" (", length(report$normalized_variables), "个变量)"), style = "color: #6c757d;")
            }
          )
        },
        if (!is.null(report$outliers_removed) && report$outliers_removed && report$outliers_count > 0) {
          div(
            style = "margin-bottom: 10px;",
            strong("异常值处理: "), span(paste("移除", report$outliers_count, "个异常值"), style = "color: #dc3545;")
          )
        }
      ),

      # 变量变化
      if (length(report$excluded_variables) > 0 || length(report$added_variables) > 0) {
        div(
          style = "background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #ffc107;",
          h5("变量变化", style = "margin: 0 0 15px 0; color: #856404; font-weight: bold;"),
          if (length(report$excluded_variables) > 0) {
            div(
              style = "margin-bottom: 10px;",
              strong("排除变量: "),
              span(paste(report$excluded_variables, collapse = ", "), style = "color: #dc3545; font-family: monospace;")
            )
          },
          if (length(report$added_variables) > 0) {
            div(
              strong("新增变量: "),
              span(paste(report$added_variables, collapse = ", "), style = "color: #28a745; font-family: monospace;")
            )
          }
        )
      },

      # 处理时间
      div(
        style = "text-align: right; color: #6c757d; font-size: 12px; margin-top: 15px;",
        paste("报告生成时间:", ifelse(is.null(report$processing_time), format(Sys.time(), "%Y-%m-%d %H:%M:%S"), format(report$processing_time, "%Y-%m-%d %H:%M:%S")))
      )
    )
  })

  # 清洗后数据下载
  output$download_cleaned_data <- downloadHandler(
    filename = function() {
      paste0("cleaned_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(values$processed_data)
      write.csv(values$processed_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )

  # 描述性统计分析
  observeEvent(input$run_descriptive, {
    # 优先使用清洗后的数据，如果没有则使用原始数据
    analysis_data <- if (!is.null(values$processed_data)) values$processed_data else values$raw_data
    req(analysis_data)

    tryCatch({
      showNotification("正在进行描述性统计分析...", type = "message", duration = NULL, id = "desc_msg")

      group_var <- if (input$desc_group_var == "") NULL else input$desc_group_var

      # 获取用户选择的选项
      show_missing <- input$desc_show_missing
      show_normal_test <- input$desc_show_normal_test
      show_variance_test <- input$desc_show_variance_test

      desc_results <- perform_descriptive_analysis(
        data = analysis_data,
        group_var = group_var,
        show_missing = show_missing,
        show_normal_test = show_normal_test,
        show_variance_test = show_variance_test
      )
      values$analysis_results$descriptive <- desc_results

      removeNotification("desc_msg")
      show_success("描述性统计分析完成！")

    }, error = function(e) {
      removeNotification("desc_msg")
      show_warning(paste("描述性统计分析失败:", e$message))
    })
  })
  
  # 输出描述性统计表格
  output$descriptive_table <- DT::renderDataTable({
    req(values$analysis_results$descriptive)

    # 创建主要的描述性统计表格
    main_table <- values$analysis_results$descriptive$table_one_df

    # 如果有额外的分析结果，创建标签页显示
    if (!is.null(values$analysis_results$descriptive$missing_analysis) ||
        !is.null(values$analysis_results$descriptive$normality_tests) ||
        !is.null(values$analysis_results$descriptive$variance_tests)) {

      # 这里先显示主表格，额外的结果将在下面的输出中显示
      DT::datatable(
        main_table,
        options = list(
          scrollX = TRUE,
          pageLength = 20,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel')
        ),
        class = "display nowrap compact",
        rownames = TRUE,
        caption = "基本描述性统计"
      )
    } else {
      DT::datatable(
        main_table,
        options = list(
          scrollX = TRUE,
          pageLength = 20,
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel')
        ),
        class = "display nowrap compact",
        rownames = TRUE
      )
    }
  })

  # 缺失值分析表格
  output$missing_analysis_table <- DT::renderDataTable({
    if (is.null(values$analysis_results$descriptive) ||
        is.null(values$analysis_results$descriptive$missing_analysis)) {
      return(NULL)
    }

    missing_stats <- values$analysis_results$descriptive$missing_analysis$missing_stats

    if (is.null(missing_stats) || nrow(missing_stats) == 0) {
      return(NULL)
    }

    DT::datatable(
      missing_stats,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE,
      caption = "缺失值分析"
    ) %>%
    DT::formatStyle(
      'Missing_Percent',
      backgroundColor = DT::styleInterval(c(5, 20, 50),
                                         c('white', '#fff3cd', '#f8d7da', '#dc3545'))
    )
  })

  # 正态性检验表格
  output$normality_tests_table <- DT::renderDataTable({
    if (is.null(values$analysis_results$descriptive) ||
        is.null(values$analysis_results$descriptive$normality_tests)) {
      return(NULL)
    }

    normality_results <- values$analysis_results$descriptive$normality_tests

    if (is.null(normality_results) || nrow(normality_results) == 0) {
      return(NULL)
    }

    DT::datatable(
      normality_results,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE,
      caption = "正态性检验结果"
    ) %>%
    DT::formatStyle(
      c('Normal_Shapiro', 'Normal_KS'),
      backgroundColor = DT::styleEqual(c('Yes', 'No'), c('#d4edda', '#f8d7da'))
    )
  })

  # 方差齐性检验表格
  output$variance_tests_table <- DT::renderDataTable({
    if (is.null(values$analysis_results$descriptive) ||
        is.null(values$analysis_results$descriptive$variance_tests)) {
      return(NULL)
    }

    variance_results <- values$analysis_results$descriptive$variance_tests

    if (is.null(variance_results) || nrow(variance_results) == 0) {
      return(NULL)
    }

    DT::datatable(
      variance_results,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE,
      caption = "方差齐性检验结果"
    ) %>%
    DT::formatStyle(
      c('Equal_Var_Levene', 'Equal_Var_Bartlett'),
      backgroundColor = DT::styleEqual(c('Yes', 'No'), c('#d4edda', '#f8d7da'))
    )
  })

  # 单因素分析
  observeEvent(input$run_univariate, {
    # 优先使用清洗后的数据，如果没有则使用原始数据
    analysis_data <- if (!is.null(values$processed_data)) values$processed_data else values$raw_data
    req(analysis_data, input$uni_outcome_var, input$uni_covariates)

    # 验证参数
    validation <- validate_analysis_params(analysis_data, input$uni_outcome_var, input$uni_covariates)
    if (!validation$valid) {
      show_warning(paste("参数验证失败:", paste(validation$errors, collapse = "; ")))
      return()
    }

    tryCatch({
      showNotification("正在进行单因素分析...", type = "message", duration = NULL, id = "uni_msg")

      uni_results <- perform_univariate_analysis(
        analysis_data,
        input$uni_outcome_var,
        input$uni_covariates
      )
      
      values$analysis_results$univariate <- uni_results
      
      removeNotification("uni_msg")
      show_success("单因素分析完成！")
      
    }, error = function(e) {
      removeNotification("uni_msg")
      show_warning(paste("单因素分析失败:", e$message))
    })
  })
  
  # 多因素分析
  observeEvent(input$run_multivariate, {
    # 添加调试信息
    log_info("多因素分析按钮被点击")
    log_info(paste("输入参数检查:"))
    log_info(paste("  - 结局变量:", input$multi_outcome_var))
    log_info(paste("  - 协变量数量:", length(input$multi_covariates)))
    log_info(paste("  - 选择方法:", input$multi_selection_method))
    
    # 优先使用清洗后的数据，如果没有则使用原始数据
    analysis_data <- if (!is.null(values$processed_data)) values$processed_data else values$raw_data
    req(analysis_data, input$multi_outcome_var, input$multi_covariates)

    log_info(paste("  - 分析数据行数:", nrow(analysis_data)))
    log_info(paste("  - 分析数据列数:", ncol(analysis_data)))

    # 验证参数
    validation <- validate_analysis_params(analysis_data, input$multi_outcome_var, input$multi_covariates)
    if (!validation$valid) {
      log_warn(paste("参数验证失败:", paste(validation$errors, collapse = "; ")))
      show_warning(paste("参数验证失败:", paste(validation$errors, collapse = "; ")))
      return()
    }

    tryCatch({
      showNotification("正在进行多因素分析...", type = "message", duration = NULL, id = "multi_msg")

      # 获取选择方法
      method <- if (is.null(input$multi_selection_method)) "enter" else input$multi_selection_method
      
      # 如果使用LASSO结果，从LASSO分析结果中获取变量
      if (method == "lasso" && !is.null(values$analysis_results$lasso)) {
        selected_vars <- values$analysis_results$lasso$selected_variables
        if (length(selected_vars) == 0) {
          show_warning("LASSO分析未选择任何变量，请先进行LASSO分析")
          return()
        }
      } else {
        selected_vars <- input$multi_covariates
      }

      log_info(paste("开始多因素分析，方法:", method, "变量数量:", length(selected_vars)))

      multi_results <- perform_multivariate_analysis(
        analysis_data,
        input$multi_outcome_var,
        selected_vars,
        method
      )
      
      values$analysis_results$multivariate <- multi_results
      
      log_info("多因素分析完成，结果已保存")
      
      removeNotification("multi_msg")
      show_success("多因素分析完成！")
      
    }, error = function(e) {
      log_error(paste("多因素分析失败:", e$message))
      removeNotification("multi_msg")
      show_warning(paste("多因素分析失败:", e$message))
    })
  })
  
  # 输出多因素分析表格
  output$multivariate_table <- DT::renderDataTable({
    req(values$analysis_results$multivariate)
    
    results_table <- values$analysis_results$multivariate$results_table
    
    DT::datatable(
      results_table,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE,
      caption = "多因素分析结果"
    ) %>%
    DT::formatStyle(
      'significance',
      backgroundColor = DT::styleEqual(c('***', '**', '*', ''), 
                                    c('#d4edda', '#c3e6cb', '#b8dacc', '#f8f9fa'))
    ) %>%
    DT::formatStyle(
      'p_value',
      color = DT::styleInterval(c(0.001, 0.01, 0.05), 
                               c('red', 'orange', 'blue', 'black'))
    )
  })
  
  # 输出多因素分析森林图
  output$multivariate_forest_plot <- renderPlot({
    req(values$analysis_results$multivariate)
    
    tryCatch({
      results_table <- values$analysis_results$multivariate$results_table
      
      if (nrow(results_table) == 0) {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "没有可显示的数据", cex = 1.2, col = "red")
        return()
      }
      
      # 创建森林图
      forest_plot <- create_multivariate_visualizations(values$analysis_results$multivariate)$forest
      
      if (!is.null(forest_plot)) {
        forest_plot
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成森林图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("森林图生成失败:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 输出模型残差图
  output$model_residuals_plot <- renderPlot({
    req(values$analysis_results$multivariate)
    
    tryCatch({
      diagnostic_plots <- create_multivariate_diagnostic_plots(values$analysis_results$multivariate)
      
      if (!is.null(diagnostic_plots)) {
        diagnostic_plots$residuals
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成残差图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("残差图生成失败:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 输出模型影响点图
  output$model_influence_plot <- renderPlot({
    req(values$analysis_results$multivariate)
    
    tryCatch({
      diagnostic_plots <- create_multivariate_diagnostic_plots(values$analysis_results$multivariate)
      
      if (!is.null(diagnostic_plots)) {
        diagnostic_plots$influence
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成影响点图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("影响点图生成失败:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # ==================== LASSO回归分析 ====================
  
  # LASSO分析执行
  observeEvent(input$run_lasso, {
    # 优先使用清洗后的数据，如果没有则使用原始数据
    analysis_data <- if (!is.null(values$processed_data)) values$processed_data else values$raw_data
    
    # 调试信息
    log_info(paste("LASSO分析触发 - 数据行数:", if (!is.null(analysis_data)) nrow(analysis_data) else "NULL"))
    log_info(paste("结局变量:", if (!is.null(input$lasso_outcome_var)) input$lasso_outcome_var else "NULL"))
    log_info(paste("协变量数量:", if (!is.null(input$lasso_covariates)) length(input$lasso_covariates) else "NULL"))
    
    req(analysis_data, input$lasso_outcome_var, input$lasso_covariates)
    
    # 显示进度通知
    lasso_msg <- showNotification(
      "正在进行LASSO分析，请稍候...", 
      type = "message", 
      duration = NULL
    )
    
    tryCatch({
      log_info("开始LASSO回归分析")
      
      # 获取参数
      outcome_var <- input$lasso_outcome_var
      covariates <- input$lasso_covariates
      alpha <- input$lasso_alpha
      cv_folds <- input$lasso_cv_folds
      lambda_selection <- input$lasso_lambda_selection
      
      # 检查协变量数量
      if (length(covariates) < 2) {
        stop("至少需要选择2个协变量进行LASSO分析")
      }
      
      # 执行LASSO分析
      lasso_results <- perform_lasso_analysis(
        data = analysis_data,
        outcome_var = outcome_var,
        covariates = covariates,
        alpha = alpha,
        cv_folds = cv_folds,
        lambda_selection = lambda_selection,
        family = "binomial"
      )
      
      # 保存结果
      values$analysis_results$lasso <- lasso_results
      
      log_info("LASSO分析完成，结果已保存")
      
      removeNotification(lasso_msg)
      show_success("LASSO分析完成！")
      
    }, error = function(e) {
      log_error(paste("LASSO分析失败:", e$message))
      removeNotification(lasso_msg)
      show_warning(paste("LASSO分析失败:", e$message))
    })
  })
  
  # 输出LASSO选择的变量
  output$lasso_selected_vars <- DT::renderDataTable({
    req(values$analysis_results$lasso)
    
    selected_vars <- values$analysis_results$lasso$selected_variables
    
    if (length(selected_vars) == 0) {
      # 如果没有选择变量，显示提示信息
      DT::datatable(
        data.frame(
          Message = "LASSO分析未选择任何变量",
          Details = "这可能表明所有变量的系数都被压缩为0"
        ),
        options = list(
          dom = 't',
          pageLength = 1
        ),
        class = "display nowrap compact",
        rownames = FALSE,
        caption = "LASSO变量选择结果"
      )
    } else {
      # 显示选择的变量
      DT::datatable(
        data.frame(
          Variable = selected_vars,
          Status = "Selected"
        ),
        options = list(
          dom = 't',
          pageLength = length(selected_vars)
        ),
        class = "display nowrap compact",
        rownames = FALSE,
        caption = paste("LASSO选择的变量 (共", length(selected_vars), "个)")
      )
    }
  })
  
  # 输出LASSO系数表
  output$lasso_coefficients_table <- DT::renderDataTable({
    req(values$analysis_results$lasso)
    
    coef_table <- values$analysis_results$lasso$coefficients
    
    DT::datatable(
      coef_table,
      options = list(
        scrollX = TRUE,
        pageLength = 20,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE,
      caption = "LASSO回归系数"
    ) %>%
    DT::formatStyle(
      'Coefficient',
      color = DT::styleInterval(c(-0.1, 0.1), 
                               c('red', 'black', 'blue'))
    ) %>%
    DT::formatStyle(
      'Variable_Type',
      backgroundColor = DT::styleEqual(c('Numeric', 'Categorical', 'Intercept'), 
                                    c('#d4edda', '#f8d7da', '#fff3cd'))
    )
  })
  
  # 输出LASSO系数路径图
  output$lasso_path_plot <- renderPlot({
    req(values$analysis_results$lasso)
    
    tryCatch({
      create_lasso_plot(values$analysis_results$lasso)
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("LASSO系数路径图生成失败:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 输出LASSO交叉验证图
  output$lasso_cv_plot <- renderPlot({
    req(values$analysis_results$lasso)
    
    tryCatch({
      create_lasso_cv_plot(values$analysis_results$lasso)
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("LASSO交叉验证图生成失败:", e$message), cex = 1.2, col = "black")
    })
  })
  
  # 输出LASSO系数条形图
  output$lasso_coefficient_plot <- renderPlot({
    req(values$analysis_results$lasso)
    
    tryCatch({
      coef_plot <- create_lasso_coefficient_plot(values$analysis_results$lasso, 
                                                title = "LASSO回归系数")
      
      if (!is.null(coef_plot)) {
        coef_plot
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成LASSO系数条形图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("LASSO系数条形图生成失败:", e$message), cex = 1.2, col = "red")
    })
  })
  
  # ==================== LASSO图形PDF下载 ====================
  
  # 下载LASSO系数路径图PDF
  output$download_lasso_path_pdf <- downloadHandler(
    filename = function() {
      paste0("LASSO_系数路径图_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$analysis_results$lasso)
        
        # 设置PDF参数
        pdf(file, width = 10, height = 8, family = "GB1")
        
        # 绘制图形
        create_lasso_plot(values$analysis_results$lasso, 
                         title = "LASSO系数路径图")
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 下载LASSO交叉验证图PDF
  output$download_lasso_cv_pdf <- downloadHandler(
    filename = function() {
      paste0("LASSO_交叉验证图_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$analysis_results$lasso)
        
        # 设置PDF参数
        pdf(file, width = 10, height = 8, family = "GB1")
        
        # 绘制图形
        create_lasso_cv_plot(values$analysis_results$lasso, 
                            title = "LASSO交叉验证图")
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 下载LASSO系数条形图PDF
  output$download_lasso_coef_pdf <- downloadHandler(
    filename = function() {
      paste0("LASSO_系数条形图_", format(Sys.time(), "%Y%m%d_%H%M%S"), ".pdf")
    },
    content = function(file) {
      tryCatch({
        req(values$analysis_results$lasso)
        
        # 设置PDF参数
        pdf(file, width = 10, height = 8, family = "GB1")
        
        # 绘制图形
        coef_plot <- create_lasso_coefficient_plot(values$analysis_results$lasso, 
                                                  title = "LASSO回归系数")
        
        if (!is.null(coef_plot)) {
          print(coef_plot)  # 对于ggplot对象，需要使用print()
        }
        
        dev.off()
        
        show_success("PDF下载完成！")
        
      }, error = function(e) {
        show_warning(paste("PDF下载失败:", e$message))
      })
    }
  )
  
  # 输出单因素分析表格
  output$univariate_table <- DT::renderDataTable({
    req(values$analysis_results$univariate)
    
    formatted_results <- format_results_table(values$analysis_results$univariate)
    
    DT::datatable(
      formatted_results,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE
    )
  })
  
  # 输出单因素分析森林图
  output$univariate_forest_plot <- renderPlot({
    req(values$analysis_results$univariate)
    
    tryCatch({
      # 获取用户配置
      alpha_level <- if (is.null(input$forest_alpha)) 0.05 else input$forest_alpha
      sort_by <- if (is.null(input$forest_sort_by)) "p_value" else input$forest_sort_by
      show_all <- if (is.null(input$forest_show_all)) TRUE else input$forest_show_all
      
      # 准备数据
      plot_data <- values$analysis_results$univariate
      
      # 根据用户选择筛选数据
      if (!show_all) {
        plot_data <- plot_data[plot_data$p_value < alpha_level, ]
      }
      
      # 根据用户选择排序
      if (sort_by == "p_value") {
        plot_data <- plot_data[order(plot_data$p_value), ]
      } else if (sort_by == "OR") {
        plot_data <- plot_data[order(plot_data$OR), ]
      } else if (sort_by == "variable") {
        plot_data <- plot_data[order(plot_data$variable), ]
      }
      
      if (nrow(plot_data) == 0) {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "没有符合条件的数据\n请调整筛选条件", cex = 1.2, col = "red")
        return()
      }
      
      forest_plot <- create_forest_plot(
        plot_data,
        title = paste0("单因素分析森林图 (α = ", alpha_level, ")"),
        or_column = "OR",
        ci_lower_column = "CI_lower",
        ci_upper_column = "CI_upper",
        p_column = "p_value"
      )
      
      if (!is.null(forest_plot)) {
        forest_plot
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成森林图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("森林图生成失败:\n", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 输出单因素分析火山图
  output$univariate_volcano_plot <- renderPlot({
    req(values$analysis_results$univariate)
    
    tryCatch({
      # 获取用户配置
      p_threshold <- if (is.null(input$volcano_p_threshold)) 0.05 else input$volcano_p_threshold
      or_threshold <- if (is.null(input$volcano_or_threshold)) 1.5 else input$volcano_or_threshold
      label_significant <- if (is.null(input$volcano_label_significant)) TRUE else input$volcano_label_significant
      
      volcano_plot <- create_volcano_plot(
        values$analysis_results$univariate,
        title = paste0("单因素分析火山图 (P < ", p_threshold, ", OR > ", or_threshold, ")"),
        or_column = "OR",
        p_column = "p_value",
        log2fc_threshold = log2(or_threshold),
        p_threshold = p_threshold
      )
      
      if (!is.null(volcano_plot)) {
        volcano_plot
      } else {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, "无法生成火山图\n请检查数据", cex = 1.2, col = "red")
      }
    }, error = function(e) {
      plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, paste("火山图生成失败:\n", e$message), cex = 1.2, col = "red")
    })
  })
  
  # 森林图更新事件监听器
  observeEvent(input$update_forest_plot, {
    # 触发森林图的重新渲染
    output$univariate_forest_plot <- renderPlot({
      req(values$analysis_results$univariate)
      
      tryCatch({
        # 获取用户配置
        alpha_level <- if (is.null(input$forest_alpha)) 0.05 else input$forest_alpha
        sort_by <- if (is.null(input$forest_sort_by)) "p_value" else input$forest_sort_by
        show_all <- if (is.null(input$forest_show_all)) TRUE else input$forest_show_all
        
        # 准备数据
        plot_data <- values$analysis_results$univariate
        
        # 根据用户选择筛选数据
        if (!show_all) {
          plot_data <- plot_data[plot_data$p_value < alpha_level, ]
        }
        
        # 根据用户选择排序
        if (sort_by == "p_value") {
          plot_data <- plot_data[order(plot_data$p_value), ]
        } else if (sort_by == "OR") {
          plot_data <- plot_data[order(plot_data$OR), ]
        } else if (sort_by == "variable") {
          plot_data <- plot_data[order(plot_data$variable), ]
        }
        
        if (nrow(plot_data) == 0) {
          plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
          text(1, 1, "没有符合条件的数据\n请调整筛选条件", cex = 1.2, col = "red")
          return()
        }
        
        forest_plot <- create_forest_plot(
          plot_data,
          title = paste0("单因素分析森林图 (α = ", alpha_level, ")"),
          or_column = "OR",
          ci_lower_column = "CI_lower",
          ci_upper_column = "CI_upper",
          p_column = "p_value"
        )
        
        if (!is.null(forest_plot)) {
          forest_plot
        } else {
          plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
          text(1, 1, "无法生成森林图\n请检查数据", cex = 1.2, col = "red")
        }
      }, error = function(e) {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, paste("森林图生成失败:\n", e$message), cex = 1.2, col = "red")
      })
    })
    
    show_success("森林图已更新")
  })
  
  # 火山图更新事件监听器
  observeEvent(input$update_volcano_plot, {
    # 触发火山图的重新渲染
    output$univariate_volcano_plot <- renderPlot({
      req(values$analysis_results$univariate)
      
      tryCatch({
        # 获取用户配置
        p_threshold <- if (is.null(input$volcano_p_threshold)) 0.05 else input$volcano_p_threshold
        or_threshold <- if (is.null(input$volcano_or_threshold)) 1.5 else input$volcano_or_threshold
        label_significant <- if (is.null(input$volcano_label_significant)) TRUE else input$volcano_label_significant
        
        volcano_plot <- create_volcano_plot(
          values$analysis_results$univariate,
          title = paste0("单因素分析火山图 (P < ", p_threshold, ", OR > ", or_threshold, ")"),
          or_column = "OR",
          p_column = "p_value",
          log2fc_threshold = log2(or_threshold),
          p_threshold = p_threshold
        )
        
        if (!is.null(volcano_plot)) {
          volcano_plot
        } else {
          plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
          text(1, 1, "无法生成火山图\n请检查数据", cex = 1.2, col = "red")
        }
      }, error = function(e) {
        plot(1, 1, type = "n", axes = FALSE, xlab = "", ylab = "")
        text(1, 1, paste("火山图生成失败:\n", e$message), cex = 1.2, col = "red")
      })
    })
    
    show_success("火山图已更新")
  })
  
  # 森林图下载处理器
  output$download_forest_plot <- downloadHandler(
    filename = function() {
      paste0("forest_plot_", Sys.Date(), ".pdf")
    },
    content = function(file) {
      tryCatch({
        # 获取用户配置
        alpha_level <- if (is.null(input$forest_alpha)) 0.05 else input$forest_alpha
        sort_by <- if (is.null(input$forest_sort_by)) "p_value" else input$forest_sort_by
        show_all <- if (is.null(input$forest_show_all)) TRUE else input$forest_show_all
        
        # 准备数据
        plot_data <- values$analysis_results$univariate
        
        # 根据用户选择筛选数据
        if (!show_all) {
          plot_data <- plot_data[plot_data$p_value < alpha_level, ]
        }
        
        # 根据用户选择排序
        if (sort_by == "p_value") {
          plot_data <- plot_data[order(plot_data$p_value), ]
        } else if (sort_by == "OR") {
          plot_data <- plot_data[order(plot_data$OR), ]
        } else if (sort_by == "variable") {
          plot_data <- plot_data[order(plot_data$variable), ]
        }
        
        if (nrow(plot_data) == 0) {
          show_warning("没有符合条件的数据")
          return()
        }
        
        # 生成森林图
        forest_plot <- create_forest_plot(
          plot_data,
          title = paste0("单因素分析森林图 (α = ", alpha_level, ")"),
          or_column = "OR",
          ci_lower_column = "CI_lower",
          ci_upper_column = "CI_upper",
          p_column = "p_value"
        )
        
        if (!is.null(forest_plot)) {
          # 保存为PDF文件
          pdf(file, width = 12, height = 8, onefile = TRUE)
          print(forest_plot)
          dev.off()
          show_success("森林图下载完成")
        } else {
          show_warning("无法生成森林图")
        }
      }, error = function(e) {
        show_warning(paste("森林图下载失败:", e$message))
      })
    }
  )
  
  # 火山图下载处理器
  output$download_volcano_plot <- downloadHandler(
    filename = function() {
      paste0("volcano_plot_", Sys.Date(), ".pdf")
    },
    content = function(file) {
      tryCatch({
        # 获取用户配置
        p_threshold <- if (is.null(input$volcano_p_threshold)) 0.05 else input$volcano_p_threshold
        or_threshold <- if (is.null(input$volcano_or_threshold)) 1.5 else input$volcano_or_threshold
        label_significant <- if (is.null(input$volcano_label_significant)) TRUE else input$volcano_label_significant
        
        # 生成火山图
        volcano_plot <- create_volcano_plot(
          values$analysis_results$univariate,
          title = paste0("单因素分析火山图 (P < ", p_threshold, ", OR > ", or_threshold, ")"),
          or_column = "OR",
          p_column = "p_value",
          log2fc_threshold = log2(or_threshold),
          p_threshold = p_threshold
        )
        
        if (!is.null(volcano_plot)) {
          # 保存为PDF文件
          pdf(file, width = 10, height = 7, onefile = TRUE)
          print(volcano_plot)
          dev.off()
          show_success("火山图下载完成")
        } else {
          show_warning("无法生成火山图")
        }
      }, error = function(e) {
        show_warning(paste("火山图下载失败:", e$message))
      })
    }
  )
  
  # 下载示例数据
  output$download_sample <- downloadHandler(
    filename = function() {
      paste0("sample_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      sample_data <- generate_sample_data(500)
      write.csv(sample_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
  
  # 快速开始按钮
  observeEvent(input$start_analysis, {
    updateTabItems(session, "sidebar_menu", "data_upload")
    show_success("请先上传您的数据文件开始分析")
  })

  # ==================== 多文件上传功能 ====================
  # 注意：多文件数据现在存储在统一的values对象中

  # 批量文件上传处理
  observeEvent(input$multi_files_batch, {
    req(input$multi_files_batch)

    tryCatch({
      showNotification("正在处理上传的文件...", type = "message", duration = NULL, id = "batch_upload_msg")

      files_info <- input$multi_files_batch

      # 清空现有文件
      values$multi_files <- list()
      values$file_counter <- 0

      # 处理每个上传的文件
      for (i in 1:nrow(files_info)) {
        file_info <- files_info[i, ]

        tryCatch({
          # 检测分隔符
          separator <- detect_separator(file_info$datapath, input$batch_separator)

          # 读取文件
          data <- read_medical_data(
            file_info$datapath,
            separator = separator,
            encoding = input$batch_encoding,
            header = input$batch_header,
            stringsAsFactors = FALSE
          )

          # 生成文件描述
          file_desc <- if (input$batch_auto_desc) {
            generate_auto_description(file_info$name, data)
          } else {
            paste("文件", i)
          }

          # 存储文件信息
          file_key <- paste0("batch_file_", i)
          values$multi_files[[file_key]] <- list(
            name = file_info$name,
            description = file_desc,
            data = data,
            upload_time = Sys.time(),
            separator = separator,
            encoding = input$batch_encoding
          )

          values$file_counter <- values$file_counter + 1

        }, error = function(e) {
          show_warning(paste("文件", file_info$name, "处理失败:", e$message))
        })
      }

      removeNotification("batch_upload_msg")
      show_success(paste("批量上传完成！成功处理", length(values$multi_files), "个文件"))

    }, error = function(e) {
      removeNotification("batch_upload_msg")
      show_warning(paste("批量上传失败:", e$message))
    })
  })

  # 添加单个文件输入
  observeEvent(input$add_single_file, {
    values$file_counter <- values$file_counter + 1
    file_id <- values$file_counter

    insertUI(
      selector = "#single_files_container",
      where = "beforeEnd",
      ui = tags$div(
        id = paste0("single_file_input_", file_id),
        class = "single-file-input",
        style = "border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f9f9f9;",

        tags$div(
          class = "row",
          tags$div(
            class = "col-md-4",
            fileInput(
              paste0("single_file_", file_id),
              paste("文件", file_id),
              accept = c(".csv", ".txt", ".tsv"),
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-4",
            textInput(
              paste0("single_desc_", file_id),
              "文件描述",
              placeholder = "请输入文件描述",
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-3",
            selectInput(
              paste0("single_sep_", file_id),
              "分隔符",
              choices = list(
                "自动检测" = "auto",
                "逗号 (,)" = ",",
                "制表符 (\\t)" = "\t",
                "分号 (;)" = ";",
                "空格" = " ",
                "竖线 (|)" = "|"
              ),
              selected = "auto",
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-1",
            tags$br(),
            actionButton(
              "remove_single_id",
              "",
              icon = icon("trash"),
              class = "btn-danger btn-sm",
              style = "margin-top: 5px;",
              onclick = paste0("Shiny.setInputValue('remove_single_id', ", file_id, ", {priority: 'event'});")
            )
          )
        )
      )
    )

    show_success(paste("已添加文件输入", file_id))
  })

  # 移除单个文件输入
  observeEvent(input$remove_single_id, {
    removeUI(selector = paste0("#single_file_input_", input$remove_single_id))

    # 从存储中移除对应文件
    file_key <- paste0("single_file_", input$remove_single_id)
    if (file_key %in% names(values$multi_files)) {
      values$multi_files[[file_key]] <- NULL
    }

    show_success("单个文件输入已移除")
  })

  # 清空所有文件
  observeEvent(input$clear_all_files, {
    # 移除所有动态创建的单个文件输入
    for (i in 1:values$file_counter) {
      removeUI(selector = paste0("#single_file_input_", i))
    }

    # 重置计数器和数据
    values$file_counter <- 0
    values$multi_files <- list()
    values$merged_data <- NULL
    values$merge_report <- NULL

    # 重置批量文件输入（通过JavaScript）
    tryCatch({
      shinyjs::reset("multi_files_batch")
    }, error = function(e) {
      # 如果shinyjs::reset失败，使用JavaScript直接重置
      session$sendCustomMessage("resetFileInput", "multi_files_batch")
    })

    show_success("所有文件已清空")
  })

  # 监听单个文件上传
  observe({
    for (i in 1:values$file_counter) {
      local({
        file_id <- paste0("single_file_", i)
        desc_id <- paste0("single_desc_", i)
        sep_id <- paste0("single_sep_", i)

        file_input <- input[[file_id]]
        file_desc <- input[[desc_id]]
        file_sep <- input[[sep_id]]

        if (!is.null(file_input)) {
          tryCatch({
            # 检测分隔符
            separator <- detect_separator(file_input$datapath, file_sep)

            # 读取文件
            data <- read_medical_data(
              file_input$datapath,
              separator = separator,
              encoding = input$batch_encoding,
              header = input$batch_header,
              stringsAsFactors = FALSE
            )

            # 存储文件信息
            values$multi_files[[file_id]] <- list(
              name = file_input$name,
              description = if (is.null(file_desc) || file_desc == "") paste("文件", i) else file_desc,
              data = data,
              upload_time = Sys.time(),
              separator = separator,
              encoding = input$batch_encoding
            )

            show_success(paste("文件", file_input$name, "上传成功"))

          }, error = function(e) {
            show_warning(paste("文件", file_input$name, "上传失败:", e$message))
          })
        }
      })
    }
  })

  # 文件状态表格输出（响应式）
  output$file_status_table <- DT::renderDataTable({
    if (length(values$multi_files) == 0) {
      data.frame(
        文件名 = "暂无文件",
        描述 = "",
        行数 = "",
        列数 = "",
        状态 = "等待上传",
        stringsAsFactors = FALSE
      )
    } else {
      status_data <- do.call(rbind, lapply(names(values$multi_files), function(key) {
        file_info <- values$multi_files[[key]]
        data.frame(
          文件名 = file_info$name,
          描述 = file_info$description,
          行数 = nrow(file_info$data),
          列数 = ncol(file_info$data),
          状态 = "已上传",
          stringsAsFactors = FALSE
        )
      }))
      status_data
    }
  }, options = list(dom = 't', pageLength = 10))

  # 文件合并处理
  observeEvent(input$merge_files, {
    req(length(values$multi_files) >= 2)

    tryCatch({
      showNotification("正在合并文件...", type = "message", duration = NULL, id = "merge_msg")

      # 执行文件合并
      merge_result <- merge_medical_files(
        file_list = values$multi_files,
        merge_key = input$merge_key,
        merge_type = input$merge_type,
        remove_duplicates = input$remove_duplicates,
        add_source_info = input$add_source
      )

      # 存储合并结果
      values$merged_data <- merge_result$data
      values$merge_report <- merge_result$report

      # 将合并后的数据传递给主数据存储，以便进入后续的数据预览和清洗流程
      values$raw_data <- merge_result$data
      values$data_source <- "multi_file"

      # 更新变量选择列表
      updateSelectInput(session, "desc_group_var",
                       choices = c("无" = "", get_variable_choices(values$raw_data)))

      removeNotification("merge_msg")
      show_success(paste("文件合并完成！最终数据:", nrow(merge_result$data), "行,", ncol(merge_result$data), "列"))

      # 提示用户可以进行后续操作
      showNotification(
        "数据合并完成！现在可以进行数据预览和清洗操作。",
        type = "message",
        duration = 5
      )

      # 显示合并结果区域
      shinyjs::show("merge_results_section")

    }, error = function(e) {
      removeNotification("merge_msg")
      show_warning(paste("文件合并失败:", e$message))
    })
  })

  # 合并数据预览
  output$merged_data_preview <- DT::renderDataTable({
    req(values$merged_data)

    DT::datatable(
      values$merged_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })

  # 合并数据下载
  output$download_merged_data <- downloadHandler(
    filename = function() {
      paste0("merged_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(values$merged_data)
      write.csv(values$merged_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )

  # 合并报告内容
  output$merge_report_content <- renderUI({
    req(values$merge_report)

    report <- values$merge_report

    # 生成报告HTML
    tags$div(
      # 基本统计
      tags$div(
        class = "row",
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("合并统计", style = "margin: 0 0 10px 0; color: #155724;"),
            tags$p(paste("合并文件数:", report$summary$total_files)),
            tags$p(paste("合并类型:", report$summary$merge_type)),
            tags$p(paste("最终行数:", report$summary$final_rows)),
            tags$p(paste("最终列数:", report$summary$final_cols))
          )
        ),
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #cce5ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("数据质量", style = "margin: 0 0 10px 0; color: #004085;"),
            tags$p(paste("缺失值比例:", paste0(round(report$quality$missing_percent, 2), "%"))),
            tags$p(paste("重复行数:", report$quality$duplicate_rows)),
            tags$p(paste("数据完整性:", paste0(round(report$quality$completeness, 2), "%")))
          )
        )
      ),

      # 文件详情
      tags$h5("文件详情", style = "color: #2c3e50; margin: 20px 0 15px 0;"),
      tags$div(
        do.call(tags$div, lapply(report$files_info, function(file_info) {
          tags$div(
            style = "border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;",
            tags$strong(file_info$name),
            tags$span(paste(" - ", file_info$description), style = "color: #666;"),
            tags$br(),
            tags$small(paste("行数:", file_info$rows, "| 列数:", file_info$cols))
          )
        }))
      )
    )
  })

  # 合并数据质量图表
  output$merged_data_quality <- renderPlot({
    req(values$merged_data)

    data <- values$merged_data

    # 创建缺失值分析图
    library(ggplot2)

    # 计算缺失值比例
    missing_data <- data.frame(
      Variable = names(data),
      Missing_Percent = sapply(data, function(x) sum(is.na(x)) / length(x) * 100),
      stringsAsFactors = FALSE
    )

    # 按缺失值比例排序
    missing_data <- missing_data[order(missing_data$Missing_Percent, decreasing = TRUE), ]
    missing_data$Variable <- factor(missing_data$Variable, levels = missing_data$Variable)

    # 创建图表
    ggplot(missing_data, aes(x = Variable, y = Missing_Percent)) +
      geom_bar(stat = "identity", fill = "#3498db", alpha = 0.7) +
      geom_text(aes(label = paste0(round(Missing_Percent, 1), "%")),
                vjust = -0.5, size = 3) +
      labs(
        title = "合并数据缺失值分析",
        x = "变量名",
        y = "缺失值比例 (%)"
      ) +
      theme_minimal() +
      theme(
        axis.text.x = element_text(angle = 45, hjust = 1),
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold")
      ) +
      ylim(0, max(missing_data$Missing_Percent) * 1.1)
  })




}
