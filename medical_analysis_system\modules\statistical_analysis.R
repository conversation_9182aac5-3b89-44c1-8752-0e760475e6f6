# 医学数据分析系统 - 统计分析模块
# Medical Data Analysis System - Statistical Analysis Module

# 单因素logistic回归分析
perform_univariate_analysis <- function(data, outcome_var, covariates = NULL) {
  tryCatch({
    log_info("开始单因素分析")
    
    # 如果未指定协变量，使用除结局变量外的所有变量
    if (is.null(covariates)) {
      covariates <- names(data)[names(data) != outcome_var]
    }
    
    # 移除只有一个唯一值的变量
    single_value_vars <- sapply(data[covariates], function(x) length(unique(x[!is.na(x)])) <= 1)
    if (any(single_value_vars)) {
      excluded_vars <- names(single_value_vars)[single_value_vars]
      log_warn(paste("排除只有单一值的变量:", paste(excluded_vars, collapse = ", ")))
      covariates <- covariates[!covariates %in% excluded_vars]
    }
    
    # 单因素分析函数
    univariate_glm <- function(var) {
      tryCatch({
        formula_str <- paste0(outcome_var, " ~ ", var)
        model <- glm(as.formula(formula_str), data = data, family = binomial())
        
        # 提取结果
        summary_model <- summary(model)
        coef_table <- summary_model$coefficients
        
        if (nrow(coef_table) > 1) {  # 确保有协变量系数
          coef_row <- coef_table[2, ]  # 第二行是协变量系数
          
          OR <- exp(coef_row[1])
          SE <- coef_row[2]
          CI_lower <- exp(coef_row[1] - 1.96 * SE)
          CI_upper <- exp(coef_row[1] + 1.96 * SE)
          p_value <- coef_row[4]
          
          # 显著性标记
          p_star <- ifelse(p_value < 0.001, "***",
                          ifelse(p_value < 0.01, "**",
                                ifelse(p_value < 0.05, "*", "")))
          
          return(data.frame(
            variable = var,
            OR = round(OR, 4),
            CI_lower = round(CI_lower, 4),
            CI_upper = round(CI_upper, 4),
            CI = paste0(round(CI_lower, 4), "-", round(CI_upper, 4)),
            p_value = round(p_value, 4),
            significance = p_star,
            stringsAsFactors = FALSE
          ))
        } else {
          return(NULL)
        }
      }, error = function(e) {
        log_warn(paste("变量", var, "单因素分析失败:", e$message))
        return(NULL)
      })
    }
    
    # 对所有协变量进行单因素分析
    results_list <- lapply(covariates, univariate_glm)
    
    # 合并结果
    results <- do.call(rbind, results_list[!sapply(results_list, is.null)])
    
    if (nrow(results) > 0) {
      # 按p值排序
      results <- results[order(results$p_value), ]
      rownames(results) <- NULL
      
      log_info(paste("单因素分析完成，共分析", nrow(results), "个变量"))
      return(results)
    } else {
      log_warn("单因素分析未产生有效结果")
      return(NULL)
    }
    
  }, error = function(e) {
    log_error(paste("单因素分析失败:", e$message))
    stop(paste("单因素分析失败:", e$message))
  })
}

# LASSO回归变量选择
perform_lasso_analysis <- function(data, outcome_var, covariates = NULL, alpha = 1, cv_folds = 5, 
                                  lambda_selection = "lambda.1se", family = "binomial") {
  tryCatch({
    log_info("开始LASSO回归分析")
    
    # 检查glmnet包
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("需要安装glmnet包，请运行: install.packages('glmnet')")
    }
    
    library(glmnet)
    
    # 数据验证
    if (is.null(data) || nrow(data) == 0) {
      stop("数据为空")
    }
    
    if (!outcome_var %in% names(data)) {
      stop("结局变量不存在")
    }
    
    # 如果没有指定协变量，使用所有非结局变量
    if (is.null(covariates)) {
      covariates <- names(data)[names(data) != outcome_var]
    }
    
    # 检查协变量是否存在
    missing_covariates <- covariates[!covariates %in% names(data)]
    if (length(missing_covariates) > 0) {
      stop(paste("以下协变量不存在:", paste(missing_covariates, collapse = ", ")))
    }
    
    # 准备数据矩阵
    x_data <- data[, covariates, drop = FALSE]
    y <- data[[outcome_var]]
    
    # 处理分类变量 - 创建虚拟变量
    categorical_vars <- names(x_data)[sapply(x_data, function(x) is.character(x) || is.factor(x))]
    numeric_vars <- names(x_data)[sapply(x_data, is.numeric)]
    
    # 处理分类变量
    if (length(categorical_vars) > 0) {
      log_info(paste("发现分类变量:", paste(categorical_vars, collapse = ", ")))
      
      # 为每个分类变量创建虚拟变量
      x_processed <- data.frame(row.names = 1:nrow(x_data))
      
      # 添加数值变量
      if (length(numeric_vars) > 0) {
        for (var in numeric_vars) {
          x_processed[[var]] <- x_data[[var]]
        }
      }
      
      # 添加分类变量的虚拟变量
      for (var in categorical_vars) {
        if (is.factor(x_data[[var]])) {
          levels_var <- levels(x_data[[var]])
        } else {
          levels_var <- unique(x_data[[var]])
        }
        
        # 跳过只有一个水平的变量
        if (length(levels_var) <= 1) {
          log_warn(paste("变量", var, "只有一个水平，将被排除"))
          next
        }
        
        # 创建虚拟变量（排除第一个水平作为参考）
        for (i in 2:length(levels_var)) {
          level_name <- levels_var[i]
          var_name <- paste0(var, "_", level_name)
          x_processed[[var_name]] <- as.numeric(x_data[[var]] == level_name)
        }
      }
      
      # 转换为矩阵
      x <- as.matrix(x_processed)
      variable_names <- colnames(x)
      
    } else {
      # 只有数值变量
      x <- as.matrix(x_data)
      variable_names <- colnames(x)
    }
    
    # 检查缺失值
    if (any(is.na(x)) || any(is.na(y))) {
      log_warn("数据中存在缺失值，将进行完整案例分析")
      complete_cases <- complete.cases(x, y)
      x <- x[complete_cases, , drop = FALSE]
      y <- y[complete_cases]
      log_info(paste("完整案例分析后，剩余", sum(complete_cases), "个观测"))
    }
    
    # 检查数据维度
    if (nrow(x) < 10) {
      stop("样本量过少，无法进行可靠的LASSO分析")
    }
    
    if (ncol(x) < 2) {
      stop("预测变量过少，无法进行LASSO分析")
    }
    
    # 标准化数值变量（但不包括虚拟变量）
    if (length(numeric_vars) > 0) {
      for (var in numeric_vars) {
        if (var %in% colnames(x)) {
          x[, var] <- scale(x[, var])
        }
      }
    }
    
    # 根据family参数确定分析类型
    if (family == "binomial") {
      # 二分类结局
      if (length(unique(y)) != 2) {
        stop("二分类结局变量应包含恰好两个不同的值")
      }
      y <- as.numeric(as.factor(y)) - 1  # 转换为0/1
      
    } else if (family == "gaussian") {
      # 连续结局
      if (!is.numeric(y)) {
        stop("连续结局变量应为数值型")
      }
      
    } else if (family == "cox") {
      # 生存分析
      if (!requireNamespace("survival", quietly = TRUE)) {
        stop("生存分析需要安装survival包")
      }
      library(survival)
      if (ncol(as.matrix(y)) != 2) {
        stop("生存分析需要时间和状态两列")
      }
    }
    
    # LASSO回归拟合
    log_info("拟合LASSO模型...")
    lasso_model <- glmnet(x, y, family = family, alpha = alpha, standardize = FALSE)
    
    # 交叉验证选择最优lambda
    log_info("进行交叉验证...")
    cv_lasso <- cv.glmnet(x, y, family = family, alpha = alpha, 
                          nfolds = cv_folds, type.measure = "class")
    
    # 选择lambda值
    if (lambda_selection == "lambda.min") {
      optimal_lambda <- cv_lasso$lambda.min
    } else {
      optimal_lambda <- cv_lasso$lambda.1se
    }
    
    # 提取系数
    lasso_coef <- coef(lasso_model, s = optimal_lambda)
    
    # 选择的变量
    selected_indices <- which(lasso_coef[, 1] != 0)
    selected_vars <- rownames(lasso_coef)[selected_indices]
    selected_vars <- selected_vars[selected_indices != "(Intercept)"]
    
    # 构建系数表
    coef_table <- data.frame(
      Variable = rownames(lasso_coef),
      Coefficient = round(lasso_coef[, 1], 6),
      stringsAsFactors = FALSE
    )
    
    # 添加变量类型信息
    coef_table$Variable_Type <- "Numeric"
    for (var in categorical_vars) {
      var_pattern <- paste0("^", var, "_")
      coef_table$Variable_Type[grepl(var_pattern, coef_table$Variable)] <- "Categorical"
    }
    coef_table$Variable_Type[coef_table$Variable == "(Intercept)"] <- "Intercept"
    
    # 计算模型性能指标
    if (family == "binomial") {
      # 预测概率
      pred_prob <- predict(lasso_model, newx = x, s = optimal_lambda, type = "response")
      pred_class <- ifelse(pred_prob > 0.5, 1, 0)
      
      # 计算AUC
      auc_value <- NA
      if (requireNamespace("pROC", quietly = TRUE)) {
        library(pROC)
        roc_obj <- roc(y, pred_prob, quiet = TRUE)
        auc_value <- auc(roc_obj)
      }
      
      # 计算分类准确率
      accuracy <- mean(pred_class == y)
      
      # 混淆矩阵
      confusion_matrix <- table(Predicted = pred_class, Actual = y)
      if (nrow(confusion_matrix) == 2 && ncol(confusion_matrix) == 2) {
        TP <- confusion_matrix[2, 2]
        TN <- confusion_matrix[1, 1]
        FP <- confusion_matrix[2, 1]
        FN <- confusion_matrix[1, 2]
        
        sensitivity <- TP / (TP + FN)
        specificity <- TN / (TN + FP)
        ppv <- TP / (TP + FP)
        npv <- TN / (TN + FN)
      } else {
        sensitivity <- specificity <- ppv <- npv <- NA
      }
      
      model_metrics <- list(
        auc = auc_value,
        accuracy = accuracy,
        sensitivity = sensitivity,
        specificity = specificity,
        ppv = ppv,
        npv = npv,
        confusion_matrix = confusion_matrix
      )
      
    } else if (family == "gaussian") {
      # 连续结局的性能指标
      pred_values <- predict(lasso_model, newx = x, s = optimal_lambda, type = "response")
      mse <- mean((y - pred_values)^2)
      rmse <- sqrt(mse)
      mae <- mean(abs(y - pred_values))
      r_squared <- 1 - sum((y - pred_values)^2) / sum((y - mean(y))^2)
      
      model_metrics <- list(
        mse = mse,
        rmse = rmse,
        mae = mae,
        r_squared = r_squared
      )
    }
    
    # 构建结果
    lasso_results <- list(
      model = lasso_model,
      cv_model = cv_lasso,
      optimal_lambda = optimal_lambda,
      lambda_min = cv_lasso$lambda.min,
      lambda_1se = cv_lasso$lambda.1se,
      selected_variables = selected_vars,
      n_selected = length(selected_vars),
      coefficients = coef_table,
      x_matrix = x,
      y_vector = y,
      variable_names = variable_names,
      categorical_vars = categorical_vars,
      numeric_vars = numeric_vars,
      family = family,
      alpha = alpha,
      cv_folds = cv_folds,
      lambda_selection = lambda_selection,
      model_metrics = model_metrics,
      data_dimensions = list(
        n_observations = nrow(x),
        n_predictors = ncol(x),
        n_categorical = length(categorical_vars),
        n_numeric = length(numeric_vars)
      )
    )
    
    log_info(paste("LASSO回归完成，选择了", length(selected_vars), "个变量"))
    log_info(paste("最优lambda:", round(optimal_lambda, 6)))
    
    return(lasso_results)
    
  }, error = function(e) {
    log_error(paste("LASSO回归分析失败:", e$message))
    stop(paste("LASSO回归分析失败:", e$message))
  })
}

# 多因素logistic回归分析
perform_multivariate_analysis <- function(data, outcome_var, selected_vars = NULL, 
                                        method = "enter") {
  tryCatch({
    log_info("开始多因素分析")
    
    # 如果未指定变量，使用所有变量
    if (is.null(selected_vars)) {
      selected_vars <- names(data)[names(data) != outcome_var]
    }
    
    # 移除只有一个唯一值的变量
    single_value_vars <- sapply(data[selected_vars], function(x) length(unique(x[!is.na(x)])) <= 1)
    if (any(single_value_vars)) {
      excluded_vars <- names(single_value_vars)[single_value_vars]
      log_warn(paste("排除只有单一值的变量:", paste(excluded_vars, collapse = ", ")))
      selected_vars <- selected_vars[!selected_vars %in% excluded_vars]
    }
    
    if (length(selected_vars) == 0) {
      stop("没有可用于多因素分析的变量")
    }
    
    # 根据方法选择变量
    if (method %in% c("forward", "backward", "stepwise")) {
      selected_vars <- perform_stepwise_selection(data, outcome_var, selected_vars, method)
    }
    
    # 构建公式
    formula_str <- paste0(outcome_var, " ~ ", paste(selected_vars, collapse = " + "))
    
    # 拟合模型
    multi_model <- glm(as.formula(formula_str), data = data, family = binomial())
    
    # 提取结果
    library(broom)
    model_summary <- summary(multi_model)
    coef_table <- model_summary$coefficients
    
    if (nrow(coef_table) > 1) {
      # 计算OR和置信区间
      OR <- exp(coef_table[-1, 1])  # 排除截距
      
      # 计算置信区间
      confint_result <- tryCatch({
        confint(multi_model)[-1, ]  # 排除截距
      }, error = function(e) {
        # 如果confint失败，使用Wald方法
        coef_vals <- coef_table[-1, 1]
        se_vals <- coef_table[-1, 2]
        cbind(coef_vals - 1.96 * se_vals, coef_vals + 1.96 * se_vals)
      })
      
      CI_lower <- exp(confint_result[, 1])
      CI_upper <- exp(confint_result[, 2])
      
      p_values <- coef_table[-1, 4]
      
      # 显著性标记
      p_star <- ifelse(p_values < 0.001, "***",
                      ifelse(p_values < 0.01, "**",
                            ifelse(p_values < 0.05, "*", "")))
      
      # 构建结果数据框
      results <- data.frame(
        variable = rownames(coef_table)[-1],
        OR = round(OR, 4),
        CI_lower = round(CI_lower, 4),
        CI_upper = round(CI_upper, 4),
        CI = paste0(round(CI_lower, 4), "-", round(CI_upper, 4)),
        p_value = round(p_values, 4),
        significance = p_star,
        stringsAsFactors = FALSE
      )
      
      # 模型评估指标
      model_metrics <- list(
        AIC = AIC(multi_model),
        BIC = BIC(multi_model),
        deviance = multi_model$deviance,
        null_deviance = multi_model$null.deviance,
        df_residual = multi_model$df.residual
      )
      
      # 返回完整结果
      multi_results <- list(
        model = multi_model,
        results_table = results,
        model_metrics = model_metrics,
        formula = formula_str,
        method = method,
        selected_variables = selected_vars
      )
      
      log_info(paste("多因素分析完成，包含", nrow(results), "个变量"))
      return(multi_results)
      
    } else {
      stop("多因素模型拟合失败")
    }
    
  }, error = function(e) {
    log_error(paste("多因素分析失败:", e$message))
    stop(paste("多因素分析失败:", e$message))
  })
}

# 逐步回归变量选择
perform_stepwise_selection <- function(data, outcome_var, candidate_vars, method = "stepwise") {
  tryCatch({
    log_info(paste("开始", method, "逐步回归变量选择"))
    
    # 构建初始模型（仅包含截距）
    null_formula <- paste0(outcome_var, " ~ 1")
    null_model <- glm(as.formula(null_formula), data = data, family = binomial())
    
    # 构建完整模型
    full_formula <- paste0(outcome_var, " ~ ", paste(candidate_vars, collapse = " + "))
    full_model <- glm(as.formula(full_formula), data = data, family = binomial())
    
    if (method == "forward") {
      # 前进法
      step_model <- step(null_model, scope = list(lower = null_model, upper = full_model), 
                        direction = "forward", trace = 0)
    } else if (method == "backward") {
      # 后退法
      step_model <- step(full_model, direction = "backward", trace = 0)
    } else if (method == "stepwise") {
      # 逐步法
      step_model <- step(null_model, scope = list(lower = null_model, upper = full_model), 
                        direction = "both", trace = 0)
    }
    
    # 提取选择的变量
    selected_vars <- names(coef(step_model))[-1]  # 排除截距
    
    log_info(paste(method, "逐步回归完成，选择了", length(selected_vars), "个变量"))
    return(selected_vars)
    
  }, error = function(e) {
    log_warn(paste("逐步回归失败，使用所有候选变量:", e$message))
    return(candidate_vars)
  })
}

# 多因素分析模型比较
compare_multivariate_models <- function(data, outcome_var, model_configs) {
  tryCatch({
    log_info("开始多因素分析模型比较")
    
    models <- list()
    model_results <- list()
    
    for (i in seq_along(model_configs)) {
      config <- model_configs[[i]]
      model_name <- config$name
      
      tryCatch({
        # 拟合模型
        if (config$method == "enter") {
          model <- perform_multivariate_analysis(data, outcome_var, config$variables, "enter")
        } else {
          model <- perform_multivariate_analysis(data, outcome_var, config$variables, config$method)
        }
        
        models[[model_name]] <- model
        model_results[[model_name]] <- list(
          AIC = model$model_metrics$AIC,
          BIC = model$model_metrics$BIC,
          deviance = model$model_metrics$deviance,
          n_variables = length(model$selected_variables),
          variables = model$selected_variables
        )
        
      }, error = function(e) {
        log_warn(paste("模型", model_name, "拟合失败:", e$message))
      })
    }
    
    # 创建比较表格
    comparison_df <- do.call(rbind, lapply(names(model_results), function(name) {
      result <- model_results[[name]]
      data.frame(
        Model = name,
        Variables = length(result$variables),
        AIC = round(result$AIC, 2),
        BIC = round(result$BIC, 2),
        Deviance = round(result$deviance, 2),
        stringsAsFactors = FALSE
      )
    }))
    
    # 按AIC排序
    comparison_df <- comparison_df[order(comparison_df$AIC), ]
    
    # 模型选择建议
    best_model <- comparison_df$Model[1]
    selection_advice <- list(
      best_by_aic = best_model,
      best_by_bic = comparison_df$Model[which.min(comparison_df$BIC)],
      aic_difference = comparison_df$AIC - min(comparison_df$AIC),
      bic_difference = comparison_df$BIC - min(comparison_df$BIC)
    )
    
    comparison_results <- list(
      models = models,
      comparison_table = comparison_df,
      selection_advice = selection_advice
    )
    
    log_info("多因素分析模型比较完成")
    return(comparison_results)
    
  }, error = function(e) {
    log_error(paste("模型比较失败:", e$message))
    stop(paste("模型比较失败:", e$message))
  })
}

# 交互作用分析
analyze_interactions <- function(data, outcome_var, main_vars, interaction_vars = NULL) {
  tryCatch({
    log_info("开始交互作用分析")
    
    if (is.null(interaction_vars)) {
      interaction_vars <- main_vars
    }
    
    interactions <- list()
    
    for (i in seq_along(main_vars)) {
      for (j in seq_along(interaction_vars)) {
        if (i != j) {
          var1 <- main_vars[i]
          var2 <- interaction_vars[j]
          
          # 构建交互作用模型
          interaction_formula <- paste0(outcome_var, " ~ ", var1, " * ", var2)
          
          tryCatch({
            interaction_model <- glm(as.formula(interaction_formula), data = data, family = binomial())
            
            # 提取交互作用项的结果
            coef_summary <- summary(interaction_model)$coefficients
            interaction_term <- paste0(var1, ":", var2)
            
            if (interaction_term %in% rownames(coef_summary)) {
              interaction_coef <- coef_summary[interaction_term, ]
              
              interaction_result <- list(
                variable1 = var1,
                variable2 = var2,
                interaction_term = interaction_term,
                coefficient = interaction_coef[1],
                SE = interaction_coef[2],
                OR = exp(interaction_coef[1]),
                p_value = interaction_coef[4],
                significant = interaction_coef[4] < 0.05
              )
              
              interactions[[paste0(var1, "_", var2)]] <- interaction_result
            }
            
          }, error = function(e) {
            log_warn(paste("交互作用", var1, "*", var2, "分析失败:", e$message))
          })
        }
      }
    }
    
    # 创建交互作用结果表格
    if (length(interactions) > 0) {
      interaction_df <- do.call(rbind, lapply(interactions, function(x) {
        data.frame(
          Variable1 = x$variable1,
          Variable2 = x$variable2,
          Interaction = x$interaction_term,
          Coefficient = round(x$coefficient, 4),
          SE = round(x$SE, 4),
          OR = round(x$OR, 4),
          P_value = round(x$p_value, 4),
          Significant = ifelse(x$significant, "是", "否"),
          stringsAsFactors = FALSE
        )
      }))
      
      # 按p值排序
      interaction_df <- interaction_df[order(interaction_df$P_value), ]
      
      interaction_results <- list(
        interactions = interactions,
        interaction_table = interaction_df,
        n_interactions = length(interactions),
        significant_interactions = sum(interaction_df$Significant == "是")
      )
    } else {
      interaction_results <- list(
        interactions = list(),
        interaction_table = NULL,
        n_interactions = 0,
        significant_interactions = 0
      )
    }
    
    log_info(paste("交互作用分析完成，发现", interaction_results$n_interactions, "个交互作用"))
    return(interaction_results)
    
  }, error = function(e) {
    log_error(paste("交互作用分析失败:", e$message))
    stop(paste("交互作用分析失败:", e$message))
  })
}

# 多因素分析诊断图
create_multivariate_diagnostic_plots <- function(multivariate_model) {
  tryCatch({
    log_info("开始创建多因素分析诊断图")
    
    model <- multivariate_model$model
    
    # 残差图
    residuals_plot <- ggplot(data.frame(
      fitted = fitted(model),
      residuals = residuals(model, type = "deviance")
    ), aes(x = fitted, y = residuals)) +
      geom_point(alpha = 0.6) +
      geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
      geom_smooth(method = "loess", se = TRUE, color = "blue") +
      labs(title = "残差图", x = "拟合值", y = "Deviance残差") +
      theme_minimal()
    
    # 影响点分析
    influence_data <- influence.measures(model)
    influence_plot <- ggplot(data.frame(
      index = 1:length(influence_data$infmat[, 1]),
      cook = influence_data$infmat[, "cook.d"]
    ), aes(x = index, y = cook)) +
      geom_point(alpha = 0.6) +
      geom_hline(yintercept = 4/length(influence_data$infmat[, 1]), 
                 linetype = "dashed", color = "red") +
      labs(title = "Cook's距离", x = "观测值", y = "Cook's距离") +
      theme_minimal()
    
    # Q-Q图
    qq_plot <- ggplot(data.frame(
      theoretical = qnorm(ppoints(length(residuals(model)))),
      sample = sort(residuals(model, type = "deviance"))
    ), aes(x = theoretical, y = sample)) +
      geom_point(alpha = 0.6) +
      geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "red") +
      labs(title = "Q-Q图", x = "理论分位数", y = "样本分位数") +
      theme_minimal()
    
    diagnostic_plots <- list(
      residuals = residuals_plot,
      influence = influence_plot,
      qq = qq_plot
    )
    
    log_info("多因素分析诊断图创建完成")
    return(diagnostic_plots)
    
  }, error = function(e) {
    log_error(paste("诊断图创建失败:", e$message))
    return(NULL)
  })
}

# 多因素分析结果可视化
create_multivariate_visualizations <- function(multivariate_results) {
  tryCatch({
    log_info("开始创建多因素分析可视化")
    
    results_table <- multivariate_results$results_table
    
    # 森林图
    forest_plot <- ggplot(results_table, aes(y = reorder(variable, OR))) +
      geom_point(aes(x = OR, color = p_value < 0.05), size = 3) +
      geom_errorbarh(aes(xmin = CI_lower, xmax = CI_upper, color = p_value < 0.05), 
                     height = 0.2) +
      geom_vline(xintercept = 1, linetype = "dashed", color = "red") +
      scale_color_manual(values = c("FALSE" = "gray", "TRUE" = "blue")) +
      labs(title = "多因素分析森林图", 
           x = "比值比 (OR) 及95%置信区间", 
           y = "变量",
           color = "显著性") +
      theme_minimal() +
      theme(legend.position = "bottom")
    
    # 系数图
    coefficient_plot <- ggplot(results_table, aes(y = reorder(variable, abs(log(OR))))) +
      geom_col(aes(x = log(OR), fill = p_value < 0.05), alpha = 0.7) +
      geom_vline(xintercept = 0, linetype = "dashed", color = "red") +
      scale_fill_manual(values = c("FALSE" = "gray", "TRUE" = "blue")) +
      labs(title = "多因素分析系数图", 
           x = "对数比值比 (log OR)", 
           y = "变量",
           fill = "显著性") +
      theme_minimal() +
      theme(legend.position = "bottom")
    
    # P值分布图
    pvalue_plot <- ggplot(results_table, aes(x = p_value)) +
      geom_histogram(bins = 10, fill = "steelblue", alpha = 0.7) +
      geom_vline(xintercept = 0.05, linetype = "dashed", color = "red") +
      labs(title = "P值分布", 
           x = "P值", 
           y = "频数") +
      theme_minimal()
    
    visualizations <- list(
      forest = forest_plot,
      coefficient = coefficient_plot,
      pvalue = pvalue_plot
    )
    
    log_info("多因素分析可视化创建完成")
    return(visualizations)
    
  }, error = function(e) {
    log_error(paste("可视化创建失败:", e$message))
    return(NULL)
  })
}

# 多因素分析报告生成
generate_multivariate_report <- function(multivariate_results, data_info = NULL) {
  tryCatch({
    log_info("开始生成多因素分析报告")
    
    # 基本信息
    report <- list()
    report$title <- "多因素Logistic回归分析报告"
    report$timestamp <- Sys.time()
    
    if (!is.null(data_info)) {
      report$data_info <- data_info
    }
    
    # 模型信息
    report$model_info <- list(
      formula = multivariate_results$formula,
      method = multivariate_results$method,
      n_variables = length(multivariate_results$selected_variables),
      variables = multivariate_results$selected_variables
    )
    
    # 结果摘要
    results_table <- multivariate_results$results_table
    report$results_summary <- list(
      total_variables = nrow(results_table),
      significant_variables = sum(results_table$p_value < 0.05),
      highly_significant = sum(results_table$p_value < 0.001),
      moderate_significant = sum(results_table$p_value < 0.01 & results_table$p_value >= 0.001),
      weakly_significant = sum(results_table$p_value < 0.05 & results_table$p_value >= 0.01)
    )
    
    # 模型评估
    report$model_evaluation <- multivariate_results$model_metrics
    
    # 主要发现
    significant_vars <- results_table[results_table$p_value < 0.05, ]
    if (nrow(significant_vars) > 0) {
      report$key_findings <- list(
        most_significant = significant_vars$variable[which.min(significant_vars$p_value)],
        strongest_effect = significant_vars$variable[which.max(abs(log(significant_vars$OR)))],
        n_significant = nrow(significant_vars)
      )
    }
    
    # 建议
    report$recommendations <- list(
      model_adequacy = ifelse(multivariate_results$model_metrics$deviance < 
                             multivariate_results$model_metrics$null_deviance * 0.8, 
                             "模型拟合良好", "模型拟合需要改进"),
      variable_importance = "建议关注显著性变量，特别是P < 0.01的变量",
      further_analysis = "可考虑进行交互作用分析和模型诊断"
    )
    
    log_info("多因素分析报告生成完成")
    return(report)
    
  }, error = function(e) {
    log_error(paste("报告生成失败:", e$message))
    return(NULL)
  })
}

# 模型性能评估
evaluate_model_performance <- function(model, data, outcome_var) {
  tryCatch({
    log_info("开始模型性能评估")
    
    library(pROC)
    library(Hmisc)
    library(ResourceSelection)
    
    # 预测概率
    predicted_prob <- predict(model, type = "response")
    actual_outcome <- data[[outcome_var]]
    
    # ROC分析
    roc_obj <- roc(actual_outcome, predicted_prob)
    auc_value <- auc(roc_obj)
    auc_ci <- ci.auc(roc_obj)
    
    # 最佳截断值
    best_threshold <- coords(roc_obj, "best")
    
    # C-index计算
    c_index <- rcorrcens(actual_outcome ~ predicted_prob)
    
    # Hosmer-Lemeshow拟合优度检验
    hl_test <- hoslem.test(actual_outcome, predicted_prob, g = 10)
    
    # 分类性能指标
    predicted_class <- ifelse(predicted_prob > best_threshold$threshold, 1, 0)
    
    # 混淆矩阵
    confusion_matrix <- table(Predicted = predicted_class, Actual = actual_outcome)
    
    if (nrow(confusion_matrix) == 2 && ncol(confusion_matrix) == 2) {
      TP <- confusion_matrix[2, 2]
      TN <- confusion_matrix[1, 1]
      FP <- confusion_matrix[2, 1]
      FN <- confusion_matrix[1, 2]
      
      sensitivity <- TP / (TP + FN)
      specificity <- TN / (TN + FP)
      ppv <- TP / (TP + FP)
      npv <- TN / (TN + FN)
      accuracy <- (TP + TN) / sum(confusion_matrix)
    } else {
      sensitivity <- specificity <- ppv <- npv <- accuracy <- NA
    }
    
    # 构建评估结果
    performance_metrics <- list(
      # ROC相关
      auc = round(as.numeric(auc_value), 4),
      auc_ci_lower = round(as.numeric(auc_ci[1]), 4),
      auc_ci_upper = round(as.numeric(auc_ci[3]), 4),
      auc_ci = paste0(round(as.numeric(auc_ci[1]), 4), "-", round(as.numeric(auc_ci[3]), 4)),
      
      # 最佳截断值
      best_threshold = round(best_threshold$threshold, 4),
      threshold_sensitivity = round(best_threshold$sensitivity, 4),
      threshold_specificity = round(best_threshold$specificity, 4),
      
      # C-index
      c_index = round(c_index["C Index"], 4),
      c_index_se = round(c_index["S.D."]/2, 4),
      
      # 分类性能
      sensitivity = round(sensitivity, 4),
      specificity = round(specificity, 4),
      ppv = round(ppv, 4),
      npv = round(npv, 4),
      accuracy = round(accuracy, 4),
      
      # 拟合优度
      hosmer_lemeshow_stat = round(hl_test$statistic, 4),
      hosmer_lemeshow_p = round(hl_test$p.value, 4),
      
      # 其他
      roc_object = roc_obj,
      confusion_matrix = confusion_matrix
    )
    
    log_info("模型性能评估完成")
    return(performance_metrics)
    
  }, error = function(e) {
    log_error(paste("模型性能评估失败:", e$message))
    stop(paste("模型性能评估失败:", e$message))
  })
}

# 描述性统计分析
perform_descriptive_analysis <- function(data, group_var = NULL, show_missing = TRUE,
                                       show_normal_test = FALSE, show_variance_test = FALSE) {
  tryCatch({
    log_info("开始描述性统计分析")

    library(tableone)

    # 调试信息：检查输入数据
    log_info(paste("输入数据维度:", nrow(data), "x", ncol(data)))
    log_info(paste("输入数据列名:", paste(names(data), collapse = ", ")))
    log_info(paste("分组变量:", ifelse(is.null(group_var), "无", group_var)))
    log_info(paste("显示缺失值分析:", show_missing))
    log_info(paste("显示正态性检验:", show_normal_test))
    log_info(paste("显示方差齐性检验:", show_variance_test))

    # 确定变量类型
    numeric_vars <- names(data)[sapply(data, is.numeric)]
    categorical_vars <- names(data)[sapply(data, function(x) is.character(x) || is.factor(x))]

    log_info(paste("数值变量数量:", length(numeric_vars)))
    log_info(paste("分类变量数量:", length(categorical_vars)))

    # 排除ID类变量和不适合统计检验的变量
    exclude_vars <- c("patient_id", "id", "ID", "subject_id", "hadm_id", "icustay_id")
    numeric_vars <- numeric_vars[!numeric_vars %in% exclude_vars]
    
    log_info(paste("排除ID变量后的数值变量:", paste(numeric_vars, collapse = ", ")))

    # 检查分组变量
    if (!is.null(group_var)) {
      if (!group_var %in% names(data)) {
        log_error(paste("指定的分组变量", group_var, "不存在于数据中"))
        stop(paste("分组变量", group_var, "不存在于数据中"))
      }
      
      # 检查分组变量的类型和唯一值
      group_values <- unique(data[[group_var]])
      log_info(paste("分组变量", group_var, "的唯一值:", paste(group_values, collapse = ", ")))
      log_info(paste("分组变量", group_var, "的类型:", class(data[[group_var]])))
    }

    # 创建Table 1
    if (!is.null(group_var) && group_var %in% names(data)) {
      # 分组描述性统计
      vars_to_include <- names(data)[names(data) != group_var]

      table_one <- CreateTableOne(
        vars = vars_to_include,
        strata = group_var,
        data = data,
        factorVars = categorical_vars[categorical_vars != group_var]
      )
    } else {
      # 整体描述性统计
      table_one <- CreateTableOne(
        vars = names(data),
        data = data,
        factorVars = categorical_vars
      )
    }

    # 转换为数据框
    table_one_df <- print(table_one, showAllLevels = TRUE, cramVars = categorical_vars)

    # 初始化结果列表
    results <- list(
      table_one = table_one,
      table_one_df = table_one_df,
      numeric_vars = numeric_vars,
      categorical_vars = categorical_vars
    )

    # 缺失值分析
    if (show_missing) {
      log_info("开始缺失值分析")
      missing_analysis <- perform_missing_analysis(data)
      results$missing_analysis <- missing_analysis
    }

    # 正态性检验
    if (show_normal_test && length(numeric_vars) > 0) {
      log_info("开始正态性检验")
      normality_tests <- perform_normality_tests(data, numeric_vars, group_var)
      results$normality_tests <- normality_tests
    }

    # 方差齐性检验
    if (show_variance_test && length(numeric_vars) > 0 && !is.null(group_var)) {
      log_info("开始方差齐性检验")
      variance_tests <- perform_variance_tests(data, numeric_vars, group_var)
      results$variance_tests <- variance_tests
    }

    log_info("描述性统计分析完成")
    return(results)

  }, error = function(e) {
    log_error(paste("描述性统计分析失败:", e$message))
    # 添加更详细的错误信息
    log_error(paste("错误详情:", toString(e)))
    log_error(paste("错误调用栈:", paste(sys.calls(), collapse = " -> ")))
    stop(paste("描述性统计分析失败:", e$message))
  })
}

# 缺失值分析
perform_missing_analysis <- function(data) {
  tryCatch({
    log_info("开始缺失值分析")

    # 计算每个变量的缺失值统计
    missing_stats <- data.frame(
      Variable = names(data),
      Missing_Count = sapply(data, function(x) sum(is.na(x))),
      Missing_Percent = round(sapply(data, function(x) sum(is.na(x)) / length(x) * 100), 2),
      Complete_Count = sapply(data, function(x) sum(!is.na(x))),
      stringsAsFactors = FALSE
    )

    # 按缺失值百分比排序
    missing_stats <- missing_stats[order(missing_stats$Missing_Percent, decreasing = TRUE), ]

    # 总体缺失值统计
    total_cells <- nrow(data) * ncol(data)
    total_missing <- sum(missing_stats$Missing_Count)

    summary_stats <- list(
      total_observations = nrow(data),
      total_variables = ncol(data),
      total_cells = total_cells,
      total_missing = total_missing,
      overall_missing_percent = round(total_missing / total_cells * 100, 2),
      variables_with_missing = sum(missing_stats$Missing_Count > 0),
      complete_cases = sum(complete.cases(data))
    )

    log_info("缺失值分析完成")
    return(list(
      missing_stats = missing_stats,
      summary_stats = summary_stats
    ))

  }, error = function(e) {
    log_error(paste("缺失值分析失败:", e$message))
    return(NULL)
  })
}

# 正态性检验
perform_normality_tests <- function(data, numeric_vars, group_var = NULL) {
  tryCatch({
    log_info("开始正态性检验")

    # 排除ID类变量和不适合统计检验的变量
    exclude_vars <- c("patient_id", "id", "ID", "subject_id", "hadm_id", "icustay_id")
    numeric_vars <- numeric_vars[!numeric_vars %in% exclude_vars]

    normality_results <- data.frame(
      Variable = character(),
      Group = character(),
      N = integer(),
      Shapiro_W = numeric(),
      Shapiro_p = numeric(),
      KS_D = numeric(),
      KS_p = numeric(),
      Normal_Shapiro = character(),
      Normal_KS = character(),
      stringsAsFactors = FALSE
    )

    for (var in numeric_vars) {
      var_data <- data[[var]]
      var_data <- var_data[!is.na(var_data)]  # 移除缺失值

      if (length(var_data) < 3) {
        next  # 跳过样本量太小的变量
      }

      # 检查是否所有值都相同
      if (length(unique(var_data)) <= 1) {
        next  # 跳过常数变量
      }

      # 检查方差是否为0
      if (var(var_data) == 0) {
        next  # 跳过方差为0的变量
      }

      if (!is.null(group_var) && group_var %in% names(data)) {
        # 分组检验
        groups <- unique(data[[group_var]])
        groups <- groups[!is.na(groups)]

        for (grp in groups) {
          grp_data <- data[data[[group_var]] == grp & !is.na(data[[group_var]]), var]
          grp_data <- grp_data[!is.na(grp_data)]

          if (length(grp_data) < 3) next

          # 检查是否所有值都相同
          if (length(unique(grp_data)) <= 1) next

          # 检查方差是否为0
          if (var(grp_data) == 0) next

          # Shapiro-Wilk检验 (适用于n<=5000)
          shapiro_result <- tryCatch({
            if (length(grp_data) <= 5000) {
              shapiro.test(grp_data)
            } else {
              list(statistic = NA, p.value = NA)
            }
          }, error = function(e) {
            list(statistic = NA, p.value = NA)
          })

          # Kolmogorov-Smirnov检验
          ks_result <- tryCatch({
            ks.test(grp_data, "pnorm", mean(grp_data), sd(grp_data))
          }, error = function(e) {
            list(statistic = NA, p.value = NA)
          })

          normality_results <- rbind(normality_results, data.frame(
            Variable = var,
            Group = as.character(grp),
            N = length(grp_data),
            Shapiro_W = round(as.numeric(shapiro_result$statistic), 4),
            Shapiro_p = round(shapiro_result$p.value, 4),
            KS_D = round(as.numeric(ks_result$statistic), 4),
            KS_p = round(ks_result$p.value, 4),
            Normal_Shapiro = ifelse(is.na(shapiro_result$p.value), "N/A",
                                   ifelse(shapiro_result$p.value > 0.05, "Yes", "No")),
            Normal_KS = ifelse(is.na(ks_result$p.value), "N/A",
                              ifelse(ks_result$p.value > 0.05, "Yes", "No")),
            stringsAsFactors = FALSE
          ))
        }
      } else {
        # 整体检验
        # Shapiro-Wilk检验
        shapiro_result <- tryCatch({
          if (length(var_data) <= 5000) {
            shapiro.test(var_data)
          } else {
            list(statistic = NA, p.value = NA)
          }
        }, error = function(e) {
          list(statistic = NA, p.value = NA)
        })

        # Kolmogorov-Smirnov检验
        ks_result <- tryCatch({
          ks.test(var_data, "pnorm", mean(var_data), sd(var_data))
        }, error = function(e) {
          list(statistic = NA, p.value = NA)
        })

        normality_results <- rbind(normality_results, data.frame(
          Variable = var,
          Group = "Overall",
          N = length(var_data),
          Shapiro_W = round(as.numeric(shapiro_result$statistic), 4),
          Shapiro_p = round(shapiro_result$p.value, 4),
          KS_D = round(as.numeric(ks_result$statistic), 4),
          KS_p = round(ks_result$p.value, 4),
          Normal_Shapiro = ifelse(is.na(shapiro_result$p.value), "N/A",
                                 ifelse(shapiro_result$p.value > 0.05, "Yes", "No")),
          Normal_KS = ifelse(is.na(ks_result$p.value), "N/A",
                            ifelse(ks_result$p.value > 0.05, "Yes", "No")),
          stringsAsFactors = FALSE
        ))
      }
    }

    log_info("正态性检验完成")
    return(normality_results)

  }, error = function(e) {
    log_error(paste("正态性检验失败:", e$message))
    return(NULL)
  })
}

# 方差齐性检验
perform_variance_tests <- function(data, numeric_vars, group_var) {
  tryCatch({
    log_info("开始方差齐性检验")

    library(car)  # for leveneTest

    # 排除ID类变量和不适合统计检验的变量
    exclude_vars <- c("patient_id", "id", "ID", "subject_id", "hadm_id", "icustay_id")
    numeric_vars <- numeric_vars[!numeric_vars %in% exclude_vars]

    # 调试信息：检查数据结构和变量
    log_info(paste("数据维度:", nrow(data), "x", ncol(data)))
    log_info(paste("数据列名:", paste(names(data), collapse = ", ")))
    log_info(paste("数值变量:", paste(numeric_vars, collapse = ", ")))
    log_info(paste("分组变量:", group_var))
    
    # 检查分组变量是否存在
    if (!group_var %in% names(data)) {
      log_error(paste("分组变量", group_var, "不存在于数据中"))
      return(NULL)
    }

    variance_results <- data.frame(
      Variable = character(),
      Groups = integer(),
      Levene_F = numeric(),
      Levene_p = numeric(),
      Bartlett_Chi2 = numeric(),
      Bartlett_p = numeric(),
      Equal_Var_Levene = character(),
      Equal_Var_Bartlett = character(),
      stringsAsFactors = FALSE
    )

    for (var in numeric_vars) {
      # 跳过不存在的变量
      if (!var %in% names(data)) {
        log_warn(paste("变量", var, "不存在于数据中，跳过"))
        next
      }

      # 调试信息：检查当前变量
      log_info(paste("处理变量:", var))
      log_info(paste("变量类型:", class(data[[var]])))
      log_info(paste("变量长度:", length(data[[var]])))
      log_info(paste("非缺失值数量:", sum(!is.na(data[[var]]))))

      # 准备数据
      test_data <- data[!is.na(data[[var]]) & !is.na(data[[group_var]]), c(var, group_var)]

      if (nrow(test_data) < 6) {
        log_warn(paste("变量", var, "的有效数据太少，跳过"))
        next
      }

      # 检查组数
      groups <- unique(test_data[[group_var]])
      if (length(groups) < 2) {
        log_warn(paste("变量", var, "的分组数不足，跳过"))
        next
      }

      # 检查每组样本量
      group_sizes <- table(test_data[[group_var]])
      if (any(group_sizes < 2)) {
        log_warn(paste("变量", var, "的某些分组样本量不足，跳过"))
        next
      }

      # 确保分组变量是因子类型
      test_data[[group_var]] <- as.factor(test_data[[group_var]])
      
      # 确保数值变量是数值类型
      if (!is.numeric(test_data[[var]])) {
        log_warn(paste("变量", var, "不是数值类型，尝试转换"))
        test_data[[var]] <- tryCatch({
          as.numeric(test_data[[var]])
        }, error = function(e) {
          log_warn(paste("变量", var, "转换为数值类型失败:", e$message))
          return(NULL)
        })
        
        if (is.null(test_data[[var]])) {
          log_warn(paste("变量", var, "转换失败，跳过"))
          next
        }
      }
      
      # 检查转换后的数据是否有效
      if (all(is.na(test_data[[var]])) || length(unique(test_data[[var]])) <= 1) {
        log_warn(paste("变量", var, "转换后数据无效，跳过"))
        next
      }

      # 检查每组是否有变异
      group_variances <- tryCatch({
        # 使用更安全的方式计算组内方差
        sapply(split(test_data[[var]], test_data[[group_var]]), function(x) {
          if (length(x) < 2) return(NA)
          var(x, na.rm = TRUE)
        })
      }, error = function(e) {
        log_warn(paste("计算组内方差失败:", e$message))
        # 如果失败，尝试使用aggregate函数
        tryCatch({
          agg_result <- aggregate(test_data[[var]], by = list(test_data[[group_var]]), 
                                FUN = function(x) var(x, na.rm = TRUE))
          setNames(agg_result$x, agg_result$Group.1)
        }, error = function(e2) {
          log_warn(paste("aggregate方法也失败:", e2$message))
          rep(NA, length(unique(test_data[[group_var]])))
        })
      })
      
      if (any(is.na(group_variances)) || any(group_variances == 0)) {
        log_warn(paste("变量", var, "的某些分组方差为0或NA，跳过"))
        next
      }

      # Levene检验 (对非正态分布更稳健)
      levene_result <- tryCatch({
        # 使用公式语法避免变量名冲突
        formula_str <- paste(var, "~", group_var)
        log_info(paste("Levene检验公式:", formula_str))
        
        # 检查数据是否适合检验
        if (nrow(test_data) < 10) {
          log_warn(paste("变量", var, "的样本量不足，跳过Levene检验"))
          list(statistic = NA, p.value = NA)
        } else {
          leveneTest(as.formula(formula_str), data = test_data)
        }
      }, error = function(e) {
        log_warn(paste("Levene检验公式方法失败:", e$message, "，尝试直接调用"))
        # 如果公式方法失败，尝试直接调用
        tryCatch({
          car::leveneTest(test_data[[var]], as.factor(test_data[[group_var]]))
        }, error = function(e2) {
          log_warn(paste("Levene检验直接调用也失败:", e2$message))
          list(statistic = NA, p.value = NA)
        })
      })

      # Bartlett检验 (假设正态分布)
      bartlett_result <- tryCatch({
        # 使用公式语法避免变量名冲突
        formula_str <- paste(var, "~", group_var)
        log_info(paste("Bartlett检验公式:", formula_str))
        
        # 检查数据是否适合检验
        if (nrow(test_data) < 10) {
          log_warn(paste("变量", var, "的样本量不足，跳过Bartlett检验"))
          list(statistic = NA, p.value = NA)
        } else {
          bartlett.test(as.formula(formula_str), data = test_data)
        }
      }, error = function(e) {
        log_warn(paste("Bartlett检验公式方法失败:", e$message, "，尝试直接调用"))
        # 如果公式方法失败，尝试直接调用
        tryCatch({
          bartlett.test(test_data[[var]], as.factor(test_data[[group_var]]))
        }, error = function(e2) {
          log_warn(paste("Bartlett检验直接调用也失败:", e2$message))
          list(statistic = NA, p.value = NA)
        })
      })

      # 提取结果 - 更安全的方式
      levene_f <- tryCatch({
        if (is.data.frame(levene_result)) {
          levene_result$`F value`[1]
        } else if (is.list(levene_result) && "statistic" %in% names(levene_result)) {
          as.numeric(levene_result$statistic)
        } else {
          NA
        }
      }, error = function(e) NA)

      levene_p <- tryCatch({
        if (is.data.frame(levene_result)) {
          levene_result$`Pr(>F)`[1]
        } else if (is.list(levene_result) && "p.value" %in% names(levene_result)) {
          levene_result$p.value
        } else {
          NA
        }
      }, error = function(e) NA)

      bartlett_chi2 <- tryCatch({
        if (is.list(bartlett_result) && "statistic" %in% names(bartlett_result)) {
          as.numeric(bartlett_result$statistic)
        } else {
          NA
        }
      }, error = function(e) NA)

      bartlett_p <- tryCatch({
        if (is.list(bartlett_result) && "p.value" %in% names(bartlett_result)) {
          bartlett_result$p.value
        } else {
          NA
        }
      }, error = function(e) NA)

      variance_results <- rbind(variance_results, data.frame(
        Variable = var,
        Groups = length(groups),
        Levene_F = round(levene_f, 4),
        Levene_p = round(levene_p, 4),
        Bartlett_Chi2 = round(bartlett_chi2, 4),
        Bartlett_p = round(bartlett_p, 4),
        Equal_Var_Levene = ifelse(is.na(levene_p), "N/A",
                                 ifelse(levene_p > 0.05, "Yes", "No")),
        Equal_Var_Bartlett = ifelse(is.na(bartlett_p), "N/A",
                                   ifelse(bartlett_p > 0.05, "Yes", "No")),
        stringsAsFactors = FALSE
      ))
    }

    log_info("方差齐性检验完成")
    return(variance_results)

  }, error = function(e) {
    log_error(paste("方差齐性检验失败:", e$message))
    # 添加更详细的错误信息
    log_error(paste("错误详情:", toString(e)))
    return(NULL)
  })
}

# 变量重要性分析
analyze_variable_importance <- function(univariate_results, multivariate_results,
                                      lasso_results = NULL) {
  tryCatch({
    log_info("开始变量重要性分析")

    # 获取所有变量
    all_vars <- c()
    if (!is.null(univariate_results)) all_vars <- c(all_vars, univariate_results$variable)
    if (!is.null(multivariate_results$results_table)) all_vars <- c(all_vars, multivariate_results$results_table$variable)
    if (!is.null(lasso_results$selected_variables)) all_vars <- c(all_vars, lasso_results$selected_variables)
    all_vars <- unique(all_vars)

    importance_df <- data.frame(
      variable = all_vars,
      univariate_p = NA,
      univariate_or = NA,
      multivariate_p = NA,
      multivariate_or = NA,
      lasso_selected = FALSE,
      importance_score = 0,
      stringsAsFactors = FALSE
    )

    # 填充单因素结果
    if (!is.null(univariate_results)) {
      for (i in 1:nrow(importance_df)) {
        var <- importance_df$variable[i]
        uni_row <- univariate_results[univariate_results$variable == var, ]
        if (nrow(uni_row) > 0) {
          importance_df$univariate_p[i] <- uni_row$p_value[1]
          importance_df$univariate_or[i] <- uni_row$OR[1]
        }
      }
    }

    # 填充多因素结果
    if (!is.null(multivariate_results$results_table)) {
      for (i in 1:nrow(importance_df)) {
        var <- importance_df$variable[i]
        multi_row <- multivariate_results$results_table[multivariate_results$results_table$variable == var, ]
        if (nrow(multi_row) > 0) {
          importance_df$multivariate_p[i] <- multi_row$p_value[1]
          importance_df$multivariate_or[i] <- multi_row$OR[1]
        }
      }
    }

    # 填充LASSO结果
    if (!is.null(lasso_results$selected_variables)) {
      importance_df$lasso_selected <- importance_df$variable %in% lasso_results$selected_variables
    }

    # 计算重要性评分
    for (i in 1:nrow(importance_df)) {
      score <- 0

      # 单因素显著性评分
      if (!is.na(importance_df$univariate_p[i])) {
        if (importance_df$univariate_p[i] < 0.001) score <- score + 4
        else if (importance_df$univariate_p[i] < 0.01) score <- score + 3
        else if (importance_df$univariate_p[i] < 0.05) score <- score + 2
      }

      # 多因素显著性评分
      if (!is.na(importance_df$multivariate_p[i])) {
        if (importance_df$multivariate_p[i] < 0.001) score <- score + 7
        else if (importance_df$multivariate_p[i] < 0.01) score <- score + 5
        else if (importance_df$multivariate_p[i] < 0.05) score <- score + 3
      }

      # LASSO选择评分
      if (importance_df$lasso_selected[i]) score <- score + 3

      importance_df$importance_score[i] <- score
    }

    # 按重要性评分排序
    importance_df <- importance_df[order(importance_df$importance_score, decreasing = TRUE), ]
    rownames(importance_df) <- NULL

    log_info("变量重要性分析完成")
    return(importance_df)

  }, error = function(e) {
    log_error(paste("变量重要性分析失败:", e$message))
    stop(paste("变量重要性分析失败:", e$message))
  })
}
