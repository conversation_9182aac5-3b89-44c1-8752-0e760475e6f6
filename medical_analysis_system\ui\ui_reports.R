# 医学数据分析系统 - 报告中心界面
# Medical Data Analysis System - Reports Center UI

# 生成报告界面
ui_generate_report <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #00b894 0%, #00a085 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("file-alt", style = "margin-right: 15px;"),
          "生成报告",
          style = "margin: 0; font-weight: 300;"
        ),
        p("创建专业的数据分析报告", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "报告配置",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          textInput(
            "report_title",
            "报告标题",
            value = "医学数据分析报告",
            placeholder = "请输入报告标题"
          ),
          
          textInput(
            "report_author",
            "作者",
            value = "数据分析师",
            placeholder = "请输入作者姓名"
          ),
          
          textAreaInput(
            "report_description",
            "报告描述",
            value = "本报告基于医学数据进行统计分析，包含描述性统计、单因素分析、多因素分析等内容。",
            rows = 3,
            placeholder = "请输入报告描述"
          ),
          
          hr(),
          
          h5("包含内容"),
          
          checkboxInput("include_data_summary", "数据概览", value = TRUE),
          checkboxInput("include_descriptive", "描述性统计", value = TRUE),
          checkboxInput("include_univariate", "单因素分析", value = TRUE),
          checkboxInput("include_multivariate", "多因素分析", value = TRUE),
          checkboxInput("include_lasso", "LASSO分析", value = FALSE),
          checkboxInput("include_plots", "统计图表", value = TRUE),
          checkboxInput("include_model_evaluation", "模型评估", value = TRUE),
          
          hr(),
          
          h5("输出格式"),
          
          checkboxGroupInput(
            "report_formats",
            NULL,
            choices = list(
              "HTML" = "html",
              "PDF" = "pdf",
              "Word" = "docx"
            ),
            selected = c("html", "pdf"),
            inline = TRUE
          ),

          div(
            style = "margin-top: 10px; margin-bottom: 15px;",
            actionButton(
              "report_formats_select_all",
              "全选格式",
              icon = icon("check-square"),
              class = "btn-sm btn-outline-primary",
              style = "margin-right: 5px;"
            ),
            actionButton(
              "report_formats_select_none",
              "反选格式",
              icon = icon("exchange-alt"),
              class = "btn-sm btn-outline-secondary"
            )
          ),
          
          hr(),
          
          selectInput(
            "report_template",
            "报告模板",
            choices = list(
              "标准模板" = "standard",
              "简洁模板" = "minimal",
              "详细模板" = "detailed",
              "学术模板" = "academic"
            ),
            selected = "standard"
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "报告预览",
        status = "info",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            style = "background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;",
            
            h4("报告结构预览", style = "color: #495057; margin-top: 0;"),
            
            div(
              style = "margin-left: 20px;",
              
              div(
                style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #007bff;",
                strong("1. 执行摘要")
              ),
              
              conditionalPanel(
                condition = "input.include_data_summary",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #28a745;",
                  strong("2. 数据概览")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_descriptive",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #ffc107;",
                  strong("3. 描述性统计")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_univariate",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #17a2b8;",
                  strong("4. 单因素分析")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_multivariate",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #6f42c1;",
                  strong("5. 多因素分析")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_lasso",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #e83e8c;",
                  strong("6. LASSO分析")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_plots",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #fd7e14;",
                  strong("7. 统计图表")
                )
              ),
              
              conditionalPanel(
                condition = "input.include_model_evaluation",
                div(
                  style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #20c997;",
                  strong("8. 模型评估")
                )
              ),
              
              div(
                style = "margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #6c757d;",
                strong("9. 结论与建议")
              )
            )
          ),
          
          hr(),
          
          div(
            style = "text-align: center;",
            actionButton(
              "generate_report",
              "生成报告",
              icon = icon("file-export"),
              class = "btn-success btn-lg",
              style = "padding: 12px 30px;"
            )
          )
        )
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "生成状态",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            id = "report_status_waiting",
            style = "text-align: center; padding: 50px;",
            icon("file-alt", style = "font-size: 64px; color: #6c757d; margin-bottom: 20px; opacity: 0.5;"),
            h3("等待生成报告", style = "color: #6c757d; margin: 0; font-weight: 300;"),
            p("请配置报告选项并点击生成报告", style = "color: #6c757d; margin: 10px 0 0 0;")
          ),
          
          div(
            id = "report_status_generating",
            style = "display: none; text-align: center; padding: 50px;",
            div(class = "spinner-border text-primary", role = "status", style = "width: 3rem; height: 3rem; margin-bottom: 20px;"),
            h3("正在生成报告...", style = "color: #007bff; margin: 0; font-weight: 300;"),
            p("请稍候，这可能需要几分钟时间", style = "color: #6c757d; margin: 10px 0 0 0;")
          ),
          
          div(
            id = "report_status_complete",
            style = "display: none;",
            
            div(
              style = "text-align: center; margin-bottom: 30px;",
              icon("check-circle", style = "font-size: 64px; color: #28a745; margin-bottom: 20px;"),
              h3("报告生成完成！", style = "color: #28a745; margin: 0; font-weight: 300;")
            ),
            
            div(
              id = "report_download_links",
              style = "text-align: center;",
              # 下载链接将通过JavaScript动态添加
            )
          )
        )
      )
    )
  )
)

# 报告历史界面
ui_report_history <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("history", style = "margin-right: 15px;"),
          "报告历史",
          style = "margin: 0; font-weight: 300;"
        ),
        p("查看和管理历史生成的报告", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(12,
      box(
        title = "历史报告",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            style = "margin-bottom: 20px;",
            fluidRow(
              column(4,
                dateRangeInput(
                  "report_date_range",
                  "日期范围",
                  start = Sys.Date() - 30,
                  end = Sys.Date()
                )
              ),
              column(4,
                selectInput(
                  "report_format_filter",
                  "格式筛选",
                  choices = list(
                    "全部" = "all",
                    "HTML" = "html",
                    "PDF" = "pdf",
                    "Word" = "docx"
                  ),
                  selected = "all"
                )
              ),
              column(4,
                div(
                  style = "margin-top: 25px;",
                  actionButton(
                    "refresh_reports",
                    "刷新列表",
                    icon = icon("refresh"),
                    class = "btn-info"
                  )
                )
              )
            )
          ),
          
          DT::dataTableOutput("reports_history_table")
        )
      )
    )
  )
)

# 模板管理界面
ui_templates <- fluidPage(
  fluidRow(
    column(12,
      div(
        class = "page-header",
        style = "margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #e17055 0%, #d63031 100%); 
                 border-radius: 10px; color: white;",
        h2(
          icon("file-code", style = "margin-right: 15px;"),
          "模板管理",
          style = "margin: 0; font-weight: 300;"
        ),
        p("管理和自定义报告模板", style = "margin: 10px 0 0 0; opacity: 0.9;")
      )
    )
  ),
  
  fluidRow(
    column(6,
      box(
        title = "可用模板",
        status = "primary",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            class = "template-item",
            style = "margin-bottom: 20px; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px;",
            
            h5("标准模板", style = "margin: 0 0 10px 0; color: #495057;"),
            p("包含完整的分析流程和结果展示，适合大多数医学数据分析报告。", 
              style = "margin: 0 0 10px 0; color: #6c757d; font-size: 14px;"),
            
            div(
              style = "text-align: right;",
              actionButton("preview_standard", "预览", class = "btn-info btn-sm", style = "margin-right: 5px;"),
              actionButton("edit_standard", "编辑", class = "btn-warning btn-sm", style = "margin-right: 5px;"),
              actionButton("use_standard", "使用", class = "btn-success btn-sm")
            )
          ),
          
          div(
            class = "template-item",
            style = "margin-bottom: 20px; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px;",
            
            h5("简洁模板", style = "margin: 0 0 10px 0; color: #495057;"),
            p("简化的报告格式，突出关键结果，适合快速汇报。", 
              style = "margin: 0 0 10px 0; color: #6c757d; font-size: 14px;"),
            
            div(
              style = "text-align: right;",
              actionButton("preview_minimal", "预览", class = "btn-info btn-sm", style = "margin-right: 5px;"),
              actionButton("edit_minimal", "编辑", class = "btn-warning btn-sm", style = "margin-right: 5px;"),
              actionButton("use_minimal", "使用", class = "btn-success btn-sm")
            )
          ),
          
          div(
            class = "template-item",
            style = "margin-bottom: 20px; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px;",
            
            h5("学术模板", style = "margin: 0 0 10px 0; color: #495057;"),
            p("符合学术期刊要求的详细报告格式，包含方法学描述和详细统计结果。", 
              style = "margin: 0 0 10px 0; color: #6c757d; font-size: 14px;"),
            
            div(
              style = "text-align: right;",
              actionButton("preview_academic", "预览", class = "btn-info btn-sm", style = "margin-right: 5px;"),
              actionButton("edit_academic", "编辑", class = "btn-warning btn-sm", style = "margin-right: 5px;"),
              actionButton("use_academic", "使用", class = "btn-success btn-sm")
            )
          )
        )
      )
    ),
    
    column(6,
      box(
        title = "模板编辑器",
        status = "warning",
        solidHeader = TRUE,
        width = NULL,
        
        div(
          style = "padding: 20px;",
          
          div(
            id = "template_editor_placeholder",
            style = "text-align: center; padding: 100px; color: #6c757d;",
            icon("edit", style = "font-size: 64px; margin-bottom: 20px; opacity: 0.5;"),
            h4("选择模板进行编辑", style = "margin: 0; font-weight: 300;")
          ),
          
          div(
            id = "template_editor_content",
            style = "display: none;",
            
            textInput(
              "template_name",
              "模板名称",
              placeholder = "请输入模板名称"
            ),
            
            textAreaInput(
              "template_description",
              "模板描述",
              rows = 2,
              placeholder = "请输入模板描述"
            ),
            
            div(
              style = "margin: 20px 0;",
              h5("模板内容"),
              div(
                style = "border: 1px solid #ced4da; border-radius: 4px; min-height: 300px; padding: 10px; background: #f8f9fa;",
                "模板编辑器将在此处显示..."
              )
            ),
            
            div(
              style = "text-align: center;",
              actionButton("save_template", "保存模板", class = "btn-success", style = "margin-right: 10px;"),
              actionButton("cancel_edit", "取消编辑", class = "btn-secondary")
            )
          )
        )
      )
    )
  )
)
