source('quick_merge_test.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('quick_merge_test.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source("medical_analysis_system/simple_start.R")
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source("simple_start.R")
source("medical_analysis_system/test_enhanced_features.R")
source("test_enhanced_features.R")
source("validate_system.R")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('test_merge_fix_simple.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('test_system_flow.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('test_cleaning_report.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('install_packages.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('test_multivariate.R')
source('run_app.R')
source('run_app.R')
source('test_lasso.R')
source('test_lasso.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('test_styleInterval_fix.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
setwd("E:/1_WorkFile/0.MIMIC/23. Pulmonary collapse-6/R/medical_analysis_system")
source('run_app.R')
source('run_app.R')
source('run_app.R')
source('run_app.R')
