# 医学数据分析系统 - 可视化模块
# Medical Data Analysis System - Visualization Module

# 森林图绘制函数
create_forest_plot <- function(results_data, title = "Forest Plot", 
                              or_column = "OR", ci_lower_column = "CI_lower", 
                              ci_upper_column = "CI_upper", p_column = "p_value") {
  tryCatch({
    log_info("开始绘制森林图")
    
    library(forestplot)
    library(ggplot2)
    
    # 数据验证
    if (is.null(results_data) || nrow(results_data) == 0) {
      stop("森林图数据为空")
    }
    
    # 使用所有数据，不筛选显著结果
    plot_data <- results_data[order(results_data[[p_column]]), ]
    
    # 创建标签文本
    labeltext <- cbind(
      c("Variable", plot_data$variable),
      c("OR (95% CI)", paste0(round(plot_data[[or_column]], 3), " (", 
                             round(plot_data[[ci_lower_column]], 3), "-",
                             round(plot_data[[ci_upper_column]], 3), ")")),
      c("P-value", format_pvalue(plot_data[[p_column]]))
    )
    
    # 根据p值设置颜色
    p_values <- plot_data[[p_column]]
    colors <- ifelse(p_values < 0.001, "red",
                    ifelse(p_values < 0.01, "orange",
                           ifelse(p_values < 0.05, "yellow", "gray")))
    
    # 创建森林图
    forest_plot <- forestplot(
      labeltext,
      mean = c(NA, plot_data[[or_column]]),
      lower = c(NA, plot_data[[ci_lower_column]]),
      upper = c(NA, plot_data[[ci_upper_column]]),
      title = title,
      zero = 1,
      boxsize = 0.4,
      graph.pos = 2,
      col = fpColors(box = colors, line = colors, zero = "gray50"),
      cex = 0.9,
      lineheight = "auto",
      colgap = unit(8, "mm"),
      lwd.ci = 2,
      ci.vertices = TRUE,
      ci.vertices.height = 0.4,
      clip = c(0.1, 10),
      xticks = c(0.1, 0.5, 1, 2, 5, 10),
      xlog = TRUE,
      txt_gp = fpTxtGp(
        label = gpar(cex = 0.8),
        ticks = gpar(cex = 0.8),
        xlab = gpar(cex = 1.0),
        title = gpar(cex = 1.2, fontface = "bold")
      )
    )
    
    log_info("森林图绘制完成")
    return(forest_plot)
    
  }, error = function(e) {
    log_error(paste("森林图绘制失败:", e$message))
    return(NULL)
  })
}

# ROC曲线绘制函数
create_roc_plot <- function(models_list, labels = NULL, title = "ROC Curves") {
  tryCatch({
    log_info("开始绘制ROC曲线")
    
    library(pROC)
    library(ggplot2)
    library(survminer)
    
    if (length(models_list) == 0) {
      stop("ROC曲线模型列表为空")
    }
    
    # 如果labels为空，创建默认标签
    if (is.null(labels)) {
      labels <- paste("Model", seq_along(models_list))
    }
    
    # 创建ROC对象列表
    roc_list <- list()
    auc_values <- numeric()
    
    for (i in seq_along(models_list)) {
      model <- models_list[[i]]
      if ("roc_object" %in% names(model)) {
        roc_list[[i]] <- model$roc_object
        auc_values[i] <- round(model$auc, 3)
      } else {
        log_warn(paste("模型", i, "缺少ROC对象"))
        next
      }
    }
    
    # 更新标签包含AUC值
    updated_labels <- paste0(labels, " (AUC = ", auc_values, ")")
    
    # 使用ggplot2绘制ROC曲线
    roc_plot <- ggroc(roc_list, aes = c("color"), legacy.axes = TRUE) +
      labs(
        x = "1 - Specificity",
        y = "Sensitivity",
        title = title,
        color = "Models"
      ) +
      scale_color_discrete(labels = updated_labels) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom",
        legend.title = element_text(size = 12),
        legend.text = element_text(size = 10),
        axis.title = element_text(size = 12),
        axis.text = element_text(size = 10),
        panel.grid.major = element_line(color = "gray90"),
        panel.grid.minor = element_blank()
      ) +
      geom_abline(intercept = 0, slope = 1, color = "gray", linetype = "dashed") +
      coord_fixed()
    
    log_info("ROC曲线绘制完成")
    return(roc_plot)
    
  }, error = function(e) {
    log_error(paste("ROC曲线绘制失败:", e$message))
    return(NULL)
  })
}

# 校准曲线绘制函数
create_calibration_plot <- function(model, data, outcome_var, 
                                   title = "Calibration Plot", n_groups = 10) {
  tryCatch({
    log_info("开始绘制校准曲线")
    
    library(rms)
    library(ggplot2)
    
    # 获取预测概率
    predicted_prob <- predict(model, type = "response")
    actual_outcome <- data[[outcome_var]]
    
    # 创建分组
    prob_groups <- cut(predicted_prob, breaks = quantile(predicted_prob, 
                      probs = seq(0, 1, length.out = n_groups + 1)), 
                      include.lowest = TRUE)
    
    # 计算每组的观察率和预测率
    calibration_data <- data.frame(
      group = levels(prob_groups),
      predicted = tapply(predicted_prob, prob_groups, mean, na.rm = TRUE),
      observed = tapply(actual_outcome, prob_groups, mean, na.rm = TRUE),
      n = table(prob_groups)
    )
    
    # 移除缺失值
    calibration_data <- calibration_data[complete.cases(calibration_data), ]
    
    # 绘制校准曲线
    calibration_plot <- ggplot(calibration_data, aes(x = predicted, y = observed)) +
      geom_point(aes(size = n), color = "steelblue", alpha = 0.7) +
      geom_smooth(method = "loess", se = TRUE, color = "red", linetype = "solid") +
      geom_abline(intercept = 0, slope = 1, color = "gray", linetype = "dashed") +
      labs(
        x = "Predicted Probability",
        y = "Observed Probability", 
        title = title,
        size = "Group Size"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        legend.position = "bottom",
        axis.title = element_text(size = 12),
        axis.text = element_text(size = 10),
        panel.grid.major = element_line(color = "gray90"),
        panel.grid.minor = element_blank()
      ) +
      coord_fixed() +
      xlim(0, 1) +
      ylim(0, 1)
    
    log_info("校准曲线绘制完成")
    return(calibration_plot)
    
  }, error = function(e) {
    log_error(paste("校准曲线绘制失败:", e$message))
    return(NULL)
  })
}

# 决策曲线分析图
create_dca_plot <- function(models_list, data, outcome_var, 
                           title = "Decision Curve Analysis") {
  tryCatch({
    log_info("开始绘制决策曲线")
    
    library(rmda)
    library(ggplot2)
    
    # 创建决策曲线对象列表
    dca_list <- list()
    
    for (i in seq_along(models_list)) {
      model_name <- names(models_list)[i]
      if (is.null(model_name)) model_name <- paste("Model", i)
      
      model <- models_list[[i]]
      
      # 构建公式
      if ("formula" %in% names(model)) {
        formula_str <- model$formula
      } else {
        # 尝试从模型对象中提取公式
        formula_str <- paste(outcome_var, "~", 
                           paste(names(coef(model$model))[-1], collapse = " + "))
      }
      
      # 创建决策曲线
      dca_obj <- decision_curve(
        as.formula(formula_str),
        data = data,
        family = binomial(link = 'logit'),
        study.design = "cohort",
        thresholds = seq(0, 1, by = 0.01),
        confidence.intervals = 0.95
      )
      
      dca_list[[model_name]] <- dca_obj
    }
    
    # 绘制决策曲线
    dca_plot <- plot_decision_curve(
      dca_list,
      curve.names = names(dca_list),
      xlim = c(0, 1),
      ylim = c(-0.2, 1),
      confidence.intervals = FALSE,
      legend.position = "topright",
      cost.benefit.axis = FALSE
    )
    
    log_info("决策曲线绘制完成")
    return(dca_plot)
    
  }, error = function(e) {
    log_error(paste("决策曲线绘制失败:", e$message))
    return(NULL)
  })
}

# LASSO系数路径图
create_lasso_plot <- function(lasso_results, title = "LASSO Coefficient Path") {
  tryCatch({
    log_info("开始绘制LASSO系数路径图")
    
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("需要安装glmnet包")
    }
    
    library(glmnet)
    
    if (is.null(lasso_results$model)) {
      stop("LASSO结果中缺少模型对象")
    }
    
    # 创建系数路径图
    plot(lasso_results$model, xvar = "lambda", label = TRUE, 
         main = title, xlab = "log(Lambda)", ylab = "Coefficients")
    
    # 添加最优lambda线
    if (!is.null(lasso_results$optimal_lambda)) {
      abline(v = log(lasso_results$optimal_lambda), col = "red", lty = 2, lwd = 2)
      text(log(lasso_results$optimal_lambda), max(coef(lasso_results$model)), 
           paste("Optimal λ =", round(lasso_results$optimal_lambda, 4)), 
           pos = 2, col = "red", cex = 0.8)
    }
    
    # 添加lambda.min线
    if (!is.null(lasso_results$lambda_min)) {
      abline(v = log(lasso_results$lambda_min), col = "blue", lty = 3, lwd = 1)
      text(log(lasso_results$lambda_min), min(coef(lasso_results$model)), 
           paste("λ.min =", round(lasso_results$lambda_min, 4)), 
           pos = 4, col = "blue", cex = 0.8)
    }
    
    log_info("LASSO系数路径图绘制完成")
    # 不需要返回值，plot()函数会自动在图形设备上绘制
    
  }, error = function(e) {
    log_error(paste("LASSO系数路径图绘制失败:", e$message))
    return(FALSE)
  })
}

# LASSO交叉验证图
create_lasso_cv_plot <- function(lasso_results, title = "LASSO Cross-Validation") {
  tryCatch({
    log_info("开始绘制LASSO交叉验证图")
    
    if (!requireNamespace("glmnet", quietly = TRUE)) {
      stop("需要安装glmnet包")
    }
    
    library(glmnet)
    
    if (is.null(lasso_results$cv_model)) {
      stop("LASSO结果中缺少交叉验证模型对象")
    }
    
    # 创建交叉验证图
    plot(lasso_results$cv_model, main = title)
    
    # 添加最优lambda标记
    if (!is.null(lasso_results$optimal_lambda)) {
      abline(v = log(lasso_results$optimal_lambda), col = "red", lty = 2, lwd = 2)
      text(log(lasso_results$optimal_lambda), min(lasso_results$cv_model$cvm), 
           paste("Optimal λ =", round(lasso_results$optimal_lambda, 4)), 
           pos = 2, col = "red", cex = 0.8)
    }
    
    # 添加lambda.min标记
    if (!is.null(lasso_results$lambda_min)) {
      abline(v = log(lasso_results$lambda_min), col = "blue", lty = 3, lwd = 1)
      text(log(lasso_results$lambda_min), min(lasso_results$cv_model$cvm), 
           paste("λ.min =", round(lasso_results$lambda_min, 4)), 
           pos = 4, col = "blue", cex = 0.8)
    }
    
    log_info("LASSO交叉验证图绘制完成")
    # 不需要返回值，plot()函数会自动在图形设备上绘制
    
  }, error = function(e) {
    log_error(paste("LASSO交叉验证图绘制失败:", e$message))
    return(NULL)
  })
}

# LASSO系数条形图
create_lasso_coefficient_plot <- function(lasso_results, title = "LASSO Coefficients") {
  tryCatch({
    log_info("开始绘制LASSO系数条形图")
    
    if (!requireNamespace("ggplot2", quietly = TRUE)) {
      stop("需要安装ggplot2包")
    }
    
    library(ggplot2)
    
    if (is.null(lasso_results$coefficients)) {
      stop("LASSO结果中缺少系数表")
    }
    
    # 准备数据
    coef_data <- lasso_results$coefficients
    coef_data <- coef_data[coef_data$Variable != "(Intercept)", ]
    
    if (nrow(coef_data) == 0) {
      stop("没有非零系数可显示")
    }
    
    # 按系数绝对值排序
    coef_data$abs_coef <- abs(coef_data$Coefficient)
    coef_data <- coef_data[order(coef_data$abs_coef, decreasing = TRUE), ]
    
    # 创建条形图
    coef_plot <- ggplot(coef_data, aes(x = reorder(Variable, abs_coef), y = Coefficient, 
                                       fill = Variable_Type)) +
      geom_bar(stat = "identity", color = "black", linewidth = 0.3) +
      scale_fill_manual(values = c("Numeric" = "#3498db", "Categorical" = "#e74c3c")) +
      coord_flip() +
      labs(title = title, x = "Variables", y = "Coefficients", fill = "Variable Type") +
      theme_minimal() +
      theme(
        axis.text.y = element_text(size = 10),
        axis.text.x = element_text(size = 10),
        axis.title = element_text(size = 12, face = "bold"),
        plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
        legend.position = "bottom",
        panel.grid.major.y = element_blank(),
        panel.grid.minor = element_blank()
      ) +
      geom_hline(yintercept = 0, linetype = "solid", color = "black", linewidth = 0.5)
    
    log_info("LASSO系数条形图绘制完成")
    return(coef_plot)
    
  }, error = function(e) {
    log_error(paste("LASSO系数条形图绘制失败:", e$message))
    return(NULL)
  })
}

# 缺失值模式图
create_missing_pattern_plot <- function(data, title = "Missing Data Pattern") {
  tryCatch({
    log_info("开始绘制缺失值模式图")
    
    library(VIM)
    library(ggplot2)
    
    # 计算缺失值比例
    missing_prop <- sapply(data, function(x) sum(is.na(x)) / length(x))
    missing_prop <- missing_prop[missing_prop > 0]
    
    if (length(missing_prop) == 0) {
      log_info("数据中无缺失值")
      return(NULL)
    }
    
    # 创建缺失值模式图
    missing_plot <- aggr(data, 
                        col = c('navyblue', 'red'),
                        numbers = TRUE,
                        sortVars = TRUE,
                        labels = names(data),
                        cex.axis = 0.7,
                        gap = 3,
                        ylab = c("Missing Data Histogram", "Pattern"))
    
    log_info("缺失值模式图绘制完成")
    return(missing_plot)
    
  }, error = function(e) {
    log_error(paste("缺失值模式图绘制失败:", e$message))
    return(NULL)
  })
}

# 数据分布图
create_distribution_plot <- function(data, title = "数据分布分析", max_vars = 8) {
  tryCatch({
    log_info("开始绘制数据分布图")
    
    if (!requireNamespace("ggplot2", quietly = TRUE)) {
      stop("需要安装ggplot2包")
    }
    
    library(ggplot2)
    
    # 分离数值变量和分类变量
    numeric_vars <- names(data)[sapply(data, function(x) is.numeric(x) && !is.logical(x))]
    categorical_vars <- names(data)[sapply(data, function(x) is.character(x) || is.factor(x) || is.logical(x))]
    
    # 调试信息
    log_info(paste("检测到数值变量:", length(numeric_vars), "个:", paste(numeric_vars, collapse = ", ")))
    log_info(paste("检测到分类变量:", length(categorical_vars), "个:", paste(categorical_vars, collapse = ", ")))
    
    # 限制变量数量以避免图形过于复杂
    if (length(numeric_vars) > max_vars) {
      numeric_vars <- numeric_vars[1:max_vars]
      log_warn(paste("数值变量过多，只显示前", max_vars, "个"))
    }
    
    if (length(categorical_vars) > max_vars) {
      categorical_vars <- categorical_vars[1:max_vars]
      log_warn(paste("分类变量过多，只显示前", max_vars, "个"))
    }
    
    # 创建图形列表
    plot_list <- list()
    
    # 数值变量分布图（直方图）
    if (length(numeric_vars) > 0) {
      for (i in seq_along(numeric_vars)) {
        var_name <- numeric_vars[i]
        var_data <- data[[var_name]]
        
        # 确保数据类型正确
        if (!is.numeric(var_data) || is.logical(var_data)) {
          next
        }
        
        # 移除缺失值和无穷值
        var_data_clean <- var_data[!is.na(var_data) & is.finite(var_data)]
        
        if (length(var_data_clean) > 0 && length(unique(var_data_clean)) > 1) {
          # 创建直方图
          p <- ggplot(data.frame(x = var_data_clean), aes(x = x)) +
            geom_histogram(bins = min(30, length(unique(var_data_clean))), 
                          fill = "#3498db", color = "black", alpha = 0.7) +
            labs(title = paste("分布:", var_name),
                 x = var_name, y = "频数") +
            theme_minimal() +
            theme(
              plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
              axis.text = element_text(size = 10),
              axis.title = element_text(size = 11)
            )
          
          plot_list[[paste0("num_", i)]] <- p
        }
      }
    }
    
    # 分类变量分布图（条形图）
    if (length(categorical_vars) > 0) {
      for (i in seq_along(categorical_vars)) {
        var_name <- categorical_vars[i]
        var_data <- data[[var_name]]
        
        # 确保数据类型正确
        if (!(is.character(var_data) || is.factor(var_data) || is.logical(var_data))) {
          next
        }
        
        # 移除缺失值
        var_data_clean <- var_data[!is.na(var_data)]
        
        if (length(var_data_clean) > 0) {
          # 计算频数
          freq_table <- table(var_data_clean)
          freq_df <- data.frame(
            Category = names(freq_table),
            Count = as.numeric(freq_table)
          )
          
          # 按频数排序
          freq_df <- freq_df[order(freq_df$Count, decreasing = TRUE), ]
          
          # 限制显示的类别数量
          if (nrow(freq_df) > 15) {
            freq_df <- freq_df[1:15, ]
            log_warn(paste("变量", var_name, "类别过多，只显示前15个"))
          }
          
          # 创建条形图
          p <- ggplot(freq_df, aes(x = reorder(Category, Count), y = Count)) +
            geom_bar(stat = "identity", fill = "#2ecc71", color = "black", alpha = 0.7) +
            coord_flip() +
            labs(title = paste("分布:", var_name),
                 x = "类别", y = "频数") +
            theme_minimal() +
            theme(
              plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
              axis.text = element_text(size = 10),
              axis.title = element_text(size = 11)
            )
          
          plot_list[[paste0("cat_", i)]] <- p
        }
      }
    }
    
    if (length(plot_list) == 0) {
      log_warn("没有可绘制的变量")
      return(NULL)
    }
    
    # 创建组合图形
    if (length(plot_list) == 1) {
      final_plot <- plot_list[[1]]
    } else {
      # 使用patchwork包组合多个图形
      if (requireNamespace("patchwork", quietly = TRUE)) {
        library(patchwork)
        final_plot <- wrap_plots(plot_list, ncol = 2)
      } else if (requireNamespace("gridExtra", quietly = TRUE)) {
        library(gridExtra)
        final_plot <- do.call(grid.arrange, c(plot_list, ncol = 2))
      } else {
        # 如果都不可用，只显示第一个图形
        final_plot <- plot_list[[1]]
        log_warn("patchwork和gridExtra包不可用，只显示第一个图形")
      }
    }
    
    log_info(paste("数据分布图绘制完成，共", length(plot_list), "个变量"))
    return(final_plot)
    
  }, error = function(e) {
    log_error(paste("数据分布图绘制失败:", e$message))
    return(NULL)
  })
}

# 相关性热图
create_correlation_heatmap <- function(data, title = "Correlation Heatmap") {
  tryCatch({
    log_info("开始绘制相关性热图")
    
    library(corrplot)
    library(ggplot2)
    library(reshape2)
    
    # 选择数值变量
    numeric_data <- data[sapply(data, is.numeric)]
    
    if (ncol(numeric_data) < 2) {
      log_warn("数值变量不足，无法绘制相关性热图")
      return(NULL)
    }
    
    # 计算相关系数
    cor_matrix <- cor(numeric_data, use = "complete.obs")
    
    # 转换为长格式
    cor_melted <- melt(cor_matrix)
    
    # 绘制热图
    correlation_plot <- ggplot(cor_melted, aes(Var1, Var2, fill = value)) +
      geom_tile(color = "white") +
      scale_fill_gradient2(low = "blue", high = "red", mid = "white", 
                          midpoint = 0, limit = c(-1, 1), space = "Lab",
                          name = "Correlation") +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.text.x = element_text(angle = 45, vjust = 1, hjust = 1),
        axis.title.x = element_blank(),
        axis.title.y = element_blank(),
        panel.grid.major = element_blank(),
        panel.border = element_blank(),
        panel.background = element_blank(),
        axis.ticks = element_blank()
      ) +
      labs(title = title) +
      coord_fixed()
    
    log_info("相关性热图绘制完成")
    return(correlation_plot)
    
  }, error = function(e) {
    log_error(paste("相关性热图绘制失败:", e$message))
    return(NULL)
  })
}

# 火山图绘制函数
create_volcano_plot <- function(results_data, title = "Volcano Plot", 
                               or_column = "OR", p_column = "p_value",
                               log2fc_threshold = 0.5, p_threshold = 0.05) {
  tryCatch({
    log_info("开始绘制火山图")
    
    library(ggplot2)
    library(ggrepel)
    
    # 数据验证
    if (is.null(results_data) || nrow(results_data) == 0) {
      stop("火山图数据为空")
    }
    
    # 准备数据
    plot_data <- results_data
    
    # 计算log2(OR)
    plot_data$log2_OR <- log2(plot_data[[or_column]])
    
    # 计算-log10(p值)
    plot_data$neg_log10_p <- -log10(plot_data[[p_column]])
    
    # 添加显著性标记
    plot_data$significance <- ifelse(plot_data[[p_column]] < p_threshold & 
                                    abs(plot_data$log2_OR) > log2fc_threshold, "Significant",
                                    ifelse(plot_data[[p_column]] < p_threshold, "P-value only",
                                           ifelse(abs(plot_data$log2_OR) > log2fc_threshold, "OR only", "Not significant")))
    
    # 设置颜色
    color_map <- c("Significant" = "red", "P-value only" = "blue", 
                   "OR only" = "green", "Not significant" = "gray")
    
    # 创建火山图
    volcano_plot <- ggplot(plot_data, aes(x = log2_OR, y = neg_log10_p, 
                                         color = significance, label = variable)) +
      geom_point(size = 3, alpha = 0.7) +
      geom_text_repel(data = subset(plot_data, significance == "Significant"), 
                      size = 3, max.overlaps = 10, 
                      box.padding = 0.5, point.padding = 0.5) +
      scale_color_manual(values = color_map, name = "Significance") +
      geom_hline(yintercept = -log10(p_threshold), linetype = "dashed", color = "blue", alpha = 0.7) +
      geom_vline(xintercept = c(-log2fc_threshold, log2fc_threshold), 
                 linetype = "dashed", color = "blue", alpha = 0.7) +
      labs(
        title = title,
        x = "log2(Odds Ratio)",
        y = "-log10(P-value)",
        caption = paste("Thresholds: |log2(OR)| >", round(log2fc_threshold, 2), 
                       "and P-value <", p_threshold)
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
        axis.title = element_text(size = 12),
        axis.text = element_text(size = 10),
        legend.position = "bottom",
        legend.title = element_text(size = 11),
        legend.text = element_text(size = 10),
        panel.grid.major = element_line(color = "gray90"),
        panel.grid.minor = element_line(color = "gray95"),
        panel.border = element_rect(color = "gray", fill = NA, size = 0.5)
      ) +
      coord_cartesian(clip = "off")
    
    log_info("火山图绘制完成")
    return(volcano_plot)
    
  }, error = function(e) {
    log_error(paste("火山图绘制失败:", e$message))
    return(NULL)
  })
}
