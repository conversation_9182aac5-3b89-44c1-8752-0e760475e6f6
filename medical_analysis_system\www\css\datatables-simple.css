/* DataTables 简化样式 - 配合Perfect Scrollbar使用 */

/* 基本包装器样式 */
.dataTables_wrapper {
  position: relative;
  margin: 20px 0;
}

/* 控制元素基本样式 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  position: relative;
  z-index: 1000;
  background: white;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}



/* 长度选择器 */
.dataTables_wrapper .dataTables_length select {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
}

/* 分页按钮 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 14px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 导出按钮 */
.dt-buttons .dt-button {
  position: relative;
  z-index: 1001;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 14px;
}

.dt-buttons .dt-button:hover {
  background: #5a6fd8;
}

/* 表格头部 */
.dataTables_scrollHead {
  position: relative;
  z-index: 999;
  background: white;
}

.dataTables_scrollHead th {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  font-weight: 600;
  padding: 12px 8px;
  text-align: left;
}

/* 表格主体 */
.dataTables_scrollBody {
  position: relative;
  z-index: 1;
}

.dataTables_scrollBody table {
  border-collapse: collapse;
  width: 100%;
}

.dataTables_scrollBody td {
  padding: 8px;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dataTables_scrollBody tr:hover {
  background-color: #f8f9fa;
}

/* Perfect Scrollbar 自定义样式 */
.ps__rail-x {
  background-color: #f1f1f1 !important;
  border-radius: 4px !important;
}

.ps__thumb-x {
  background-color: #c1c1c1 !important;
  border-radius: 4px !important;
}

.ps__rail-y {
  background-color: #f1f1f1 !important;
  border-radius: 4px !important;
}

.ps__thumb-y {
  background-color: #c1c1c1 !important;
  border-radius: 4px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataTables_wrapper .dataTables_filter input {
    min-width: 150px;
  }
  
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_info {
    text-align: center;
  }
  
  .dt-buttons {
    text-align: center;
  }
  
  .dt-buttons .dt-button {
    margin-bottom: 5px;
    width: 100%;
    text-align: center;
  }
} 

/* 修复下拉选择器样式问题 */
.dataTables_wrapper .dataTables_length select {
  /* 确保下拉选项显示为单行 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  /* 设置合适的宽度，避免按钮太长 */
  min-width: 80px;
  max-width: 120px;
  
  /* 确保下拉选项正常显示 */
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  
  /* 修复z-index确保下拉选项可见 */
  z-index: 1001;
  position: relative;
}

/* 修复select元素的下拉选项显示 */
.dataTables_wrapper .dataTables_length select option {
  background: white;
  color: #495057;
  padding: 4px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保下拉选择器容器样式正确 */
.dataTables_wrapper .dataTables_length {
  display: inline-block;
  vertical-align: middle;
  margin-right: 15px;
}



/* 修复分页按钮样式 */
.dataTables_wrapper .dataTables_paginate {
  display: inline-block;
  vertical-align: middle;
  margin-left: 15px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  /* 设置合适的按钮大小 */
  padding: 6px 10px;
  margin: 0 2px;
  min-width: 32px;
  text-align: center;
  
  /* 确保样式一致 */
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  /* 修复z-index */
  z-index: 1001;
  position: relative;
}

/* 修复导出按钮样式 */
.dataTables_wrapper .dt-buttons {
  display: inline-block;
  vertical-align: middle;
  margin-right: 15px;
}

.dataTables_wrapper .dt-buttons .dt-button {
  /* 设置合适的按钮大小 */
  padding: 6px 12px;
  margin-right: 5px;
  min-width: 60px;
  text-align: center;
  
  /* 确保样式一致 */
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  
  /* 修复z-index */
  z-index: 1001;
  position: relative;
}

/* 确保所有控制元素在同一行显示 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  display: inline-block;
  vertical-align: middle;
  margin: 5px 10px 5px 0;
  white-space: nowrap;
}

/* 修复表格信息显示 */
.dataTables_wrapper .dataTables_info {
  display: inline-block;
  vertical-align: middle;
  margin: 5px 15px;
  color: #6c757d;
  font-size: 14px;
}

/* 确保下拉选择器在焦点状态下正常显示 */
.dataTables_wrapper .dataTables_length select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 修复可能的select元素宽度问题 */
.dataTables_wrapper select {
  box-sizing: border-box;
  width: auto;
}

/* 确保所有按钮和输入框的字体大小一致 */
.dataTables_wrapper input,
.dataTables_wrapper select,
.dataTables_wrapper button,
.dataTables_wrapper .paginate_button,
.dataTables_wrapper .dt-button {
  font-size: 14px;
  font-family: inherit;
} 