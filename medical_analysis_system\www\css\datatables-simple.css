/* DataTables 简化样式 - 配合Perfect Scrollbar使用 */

/* 基本包装器样式 */
.dataTables_wrapper {
  position: relative;
  margin: 20px 0;
}

/* 控制元素基本样式 */
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  position: relative;
  z-index: 1000;
  background: white;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

/* 过滤器输入框 */
.dataTables_wrapper .dataTables_filter input {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 200px;
  font-size: 14px;
}

.dataTables_wrapper .dataTables_filter input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

/* 长度选择器 */
.dataTables_wrapper .dataTables_length select {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
}

/* 分页按钮 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  position: relative;
  z-index: 1001;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 14px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 导出按钮 */
.dt-buttons .dt-button {
  position: relative;
  z-index: 1001;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 14px;
}

.dt-buttons .dt-button:hover {
  background: #5a6fd8;
}

/* 表格头部 - 简化背景颜色 */
.dataTables_scrollHead {
  position: relative;
  z-index: 999;
  background: white;
}

.dataTables_scrollHead th {
  background: #f8f9fa !important;
  color: #495057 !important;
  border: 1px solid #dee2e6 !important;
  font-weight: 600;
  padding: 12px 8px;
  text-align: left;
}

/* 表格主体 */
.dataTables_scrollBody {
  position: relative;
  z-index: 1;
}

.dataTables_scrollBody table {
  border-collapse: collapse;
  width: 100%;
}

.dataTables_scrollBody td {
  padding: 8px;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dataTables_scrollBody tr:hover {
  background-color: #f8f9fa;
}

/* Perfect Scrollbar 自定义样式 */
.ps__rail-x {
  background-color: #f1f1f1 !important;
  border-radius: 4px !important;
}

.ps__thumb-x {
  background-color: #c1c1c1 !important;
  border-radius: 4px !important;
}

.ps__rail-y {
  background-color: #f1f1f1 !important;
  border-radius: 4px !important;
}

.ps__thumb-y {
  background-color: #c1c1c1 !important;
  border-radius: 4px !important;
}

/* 修复选择器被遮挡问题 */
.dataTables_wrapper .dataTables_length select {
  z-index: 9999 !important;
}

/* 确保下拉选择器在最上层 */
.dataTables_wrapper .dataTables_length {
  z-index: 9998 !important;
}

/* 修复过滤器输入框的z-index */
.dataTables_wrapper .dataTables_filter {
  z-index: 9998 !important;
}

.dataTables_wrapper .dataTables_filter input {
  z-index: 9999 !important;
}

/* 修复分页按钮的z-index */
.dataTables_wrapper .dataTables_paginate {
  z-index: 9998 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  z-index: 9999 !important;
}

/* 修复导出按钮的z-index */
.dataTables_wrapper .dt-buttons {
  z-index: 9998 !important;
}

.dataTables_wrapper .dt-buttons .dt-button {
  z-index: 9999 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataTables_wrapper .dataTables_filter input {
    min-width: 150px;
  }
  
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_info {
    text-align: center;
  }
  
  .dt-buttons {
    text-align: center;
  }
  
  .dt-buttons .dt-button {
    margin-bottom: 5px;
    width: 100%;
    text-align: center;
  }
} 

/* 确保所有下拉选择器在最上层显示 */
.dataTables_wrapper select,
.dataTables_wrapper input,
.dataTables_wrapper button,
.dataTables_wrapper .paginate_button,
.dataTables_wrapper .dt-button {
  z-index: 9999 !important;
}

/* 修复select元素的下拉选项显示 */
.dataTables_wrapper .dataTables_length select option {
  background: white;
  color: #495057;
  z-index: 10000 !important;
}

/* 确保下拉选择器容器在最上层 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  z-index: 9998 !important;
  position: relative !important;
}

/* 修复表格头部样式，确保不遮挡其他元素 */
.dataTables_scrollHead {
  z-index: 1 !important;
}

.dataTables_scrollBody {
  z-index: 1 !important;
}

/* 确保表格包装器不会遮挡控制元素 */
.dataTables_wrapper {
  z-index: 1 !important;
}

/* 修复可能的overflow问题 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_paginate,
.dataTables_wrapper .dt-buttons {
  overflow: visible !important;
}

/* 确保下拉选择器在滚动时仍然可见 */
.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
  z-index: 10000 !important;
  position: relative !important;
}

/* 修复表格行悬停效果 */
.dataTables_scrollBody tr:hover td {
  background-color: #f8f9fa !important;
  position: relative;
  z-index: 2;
}

/* 确保表格内容不被截断 */
.dataTables_scrollBody {
  overflow: visible !important;
}

/* 修复Perfect Scrollbar可能的z-index问题 */
.ps__rail-x,
.ps__rail-y {
  z-index: 1 !important;
}

.ps__thumb-x,
.ps__thumb-y {
  z-index: 2 !important;
} 